import { supabaseClient } from '../supabase-singleton';
import { Appointment, AppointmentInsert, AppointmentUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get all appointments for a salon
 */
export async function getAppointments(salonId: string) {
  const { data, error } = await supabase
    .from('appointments')
    .select(`
      *,
      customers(name, surname, phone),
      barbers(name),
      services(name, duration)
    `)
    .eq('salon_id', salonId)
    .order('date', { ascending: true })
    .order('start_time', { ascending: true });

  if (error) throw error;
  return data;
}

/**
 * Get appointments for a salon within a date range
 */
export async function getAppointmentsInRange(salonId: string, startDate: string, endDate: string) {
  const { data, error } = await supabase
    .from('appointments')
    .select(`
      *,
      customers(name, surname, phone),
      barbers(name),
      services(name, duration)
    `)
    .eq('salon_id', salonId)
    .gte('date', startDate)
    .lte('date', endDate)
    .order('date', { ascending: true })
    .order('start_time', { ascending: true });

  if (error) throw error;
  return data;
}

/**
 * Get appointments for a specific date
 */
export async function getAppointmentsByDate(salonId: string, date: string) {
  const { data, error } = await supabase
    .from('appointments')
    .select(`
      *,
      customers(name, surname, phone),
      barbers(name),
      services(name, duration)
    `)
    .eq('salon_id', salonId)
    .eq('date', date)
    .order('start_time', { ascending: true });

  if (error) throw error;
  return data;
}

/**
 * Get appointments for a specific barber
 */
export async function getAppointmentsForBarber(barberId: string) {
  const { data, error } = await supabase
    .from('appointments')
    .select(`
      *,
      customers(name, surname, phone),
      services(name, duration)
    `)
    .eq('barber_id', barberId)
    .order('date', { ascending: true })
    .order('start_time', { ascending: true });

  if (error) throw error;
  return data;
}

/**
 * Get an appointment by ID
 */
export async function getAppointmentById(id: string) {
  const { data, error } = await supabase
    .from('appointments')
    .select(`
      *,
      customers(name, surname, phone, email),
      barbers(name),
      services(name, duration)
    `)
    .eq('id', id)
    .single();

  if (error) throw error;
  return data;
}

/**
 * Create a new appointment
 */
export async function createAppointment(appointment: AppointmentInsert) {
  const { data, error } = await supabase
    .from('appointments')
    .insert(appointment)
    .select()
    .single();

  if (error) throw error;
  return data as Appointment;
}

/**
 * Update an appointment
 */
export async function updateAppointment({ id, ...appointment }: AppointmentUpdate) {
  const { data, error } = await supabase
    .from('appointments')
    .update(appointment)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Appointment;
}

/**
 * Cancel an appointment
 */
export async function cancelAppointment(id: string) {
  const { data, error } = await supabase
    .from('appointments')
    .update({ status: 'cancelled' })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Appointment;
}

/**
 * Mark an appointment as completed
 */
export async function completeAppointment(id: string) {
  const { data, error } = await supabase
    .from('appointments')
    .update({ status: 'completed' })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Appointment;
}

/**
 * Mark an appointment as no-show
 */
export async function markAppointmentAsNoShow(id: string) {
  const { data, error } = await supabase
    .from('appointments')
    .update({ status: 'no-show' })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Appointment;
}

/**
 * Delete an appointment
 */
export async function deleteAppointment(id: string) {
  const { error } = await supabase
    .from('appointments')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}
