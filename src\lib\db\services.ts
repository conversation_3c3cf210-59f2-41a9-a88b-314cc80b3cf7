import { Service, ServiceInsert, ServiceUpdate } from './types';
import { supabaseClient } from '../supabase-singleton';

const supabase = supabaseClient;

/**
 * Get all services for a salon
 */
export async function getServices(salonId: string) {
  const { data, error } = await supabase
    .from('services')
    .select('*')
    .eq('salon_id', salonId)
    .order('sort_order', { ascending: true });

  if (error) throw error;
  return data as Service[];
}

/**
 * Get a service by ID
 */
export async function getServiceById(id: string) {
  const { data, error } = await supabase
    .from('services')
    .select('*')
    .eq('id', id)
    .single();
  
  if (error) throw error;
  return data as Service;
}

/**
 * Create a new service
 */
export async function createService(service: ServiceInsert) {
  // If sort_order is not provided, get the next available order
  if (service.sort_order === undefined) {
    const { data: maxOrderData } = await supabase
      .from('services')
      .select('sort_order')
      .eq('salon_id', service.salon_id)
      .order('sort_order', { ascending: false })
      .limit(1);

    const nextOrder = maxOrderData && maxOrderData.length > 0
      ? (maxOrderData[0].sort_order || 0) + 1
      : 1;

    service.sort_order = nextOrder;
  }

  const { data, error } = await supabase
    .from('services')
    .insert(service)
    .select()
    .single();

  if (error) throw error;
  return data as Service;
}

/**
 * Update a service
 */
export async function updateService({ id, ...service }: ServiceUpdate) {
  const { data, error } = await supabase
    .from('services')
    .update(service)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data as Service;
}

/**
 * Delete a service
 */
export async function deleteService(id: string) {
  const { error } = await supabase
    .from('services')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Update services sort order
 */
export async function updateServicesOrder(salonId: string, serviceOrders: { id: string; sort_order: number }[]) {
  const updates = serviceOrders.map(({ id, sort_order }) =>
    supabase
      .from('services')
      .update({ sort_order })
      .eq('id', id)
      .eq('salon_id', salonId) // Extra security check
  );

  const results = await Promise.all(updates);

  // Check for errors
  for (const result of results) {
    if (result.error) throw result.error;
  }

  return true;
}

/**
 * Get services that a specific barber can perform
 */
export async function getServicesForBarber(salonId: string, barberId: string) {
  const { data, error } = await supabase
    .from('services')
    .select(`
      *,
      barber_services!inner(barber_id)
    `)
    .eq('salon_id', salonId)
    .eq('barber_services.barber_id', barberId)
    .order('sort_order', { ascending: true });

  if (error) throw error;
  return data as Service[];
}
