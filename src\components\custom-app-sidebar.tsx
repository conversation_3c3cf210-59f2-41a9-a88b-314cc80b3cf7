"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Calendar,
  Users,
  Scissors,
  Clock,
  Settings,
  User,
  LogOut,
  Briefcase,
  Command,
  CalendarDays,
  DollarSign,
  ShieldCheck,
  PieChart,
  Globe,
  Gift,
  CreditCard,
  ShoppingBag,
  FileText,
} from "lucide-react";
import { toast } from "sonner";

import { useUser } from "@/contexts/UserContext";
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures";
import { NotificationPanel } from "@/components/notifications/NotificationPanel";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupLabel,
  useSidebar,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Lucide icon tipini kullanmak için
type LucideIcon = React.ComponentType<React.ComponentProps<typeof Calendar>>;

// Navigasyon öğesi tipi
type NavigationItem = {
  title: string;
  href: string;
  icon: LucideIcon;
  isActive: boolean;
};

// Navigasyon grubu tipi
type NavigationGroup = {
  label: string;
  items: NavigationItem[];
};

export function CustomAppSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const { isMobile } = useSidebar();

  // UserContext'ten kullanıcı bilgilerini al
  const { user, userRole, signOut, salon, isAdminUser } = useUser();

  // Abonelik özelliklerini al
  const { features } = useSubscriptionFeatures();

  // Çıkış işlemi
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Çıkış yaparken hata:", error);
      toast.error("Çıkış yapılırken bir hata oluştu");
    }
  };

  // Kullanıcı rolüne göre navigasyon gruplarını oluştur
  const navigationGroups = React.useMemo(() => {
    // Temel navigasyon öğeleri
    const coreItems: NavigationItem[] = [];

    if (!salon && (userRole === "new_user" || userRole === "owner")) {
      coreItems.push({
        title: "Salon Oluştur",
        href: "/dashboard/settings",
        icon: Settings,
        isActive: pathname === "/dashboard/settings",
      });
    } else if(!isAdminUser) {
      // Normal navigasyon öğeleri
      coreItems.push(
        {
          title: "Randevular",
          href: "/dashboard",
          icon: Calendar,
          isActive: pathname === "/dashboard",
        },
        {
          title: "Müşteriler",
          href: "/dashboard/customers",
          icon: User,
          isActive: pathname === "/dashboard/customers",
        }
      );
    }

    // Temel grup
    const coreGroup: NavigationGroup = {
      label: "Ana Menü",
      items: coreItems,
    };

    // Admin için grup
    if (isAdminUser) {
      const adminGroup: NavigationGroup = {
        label: "Admin Paneli",
        items: [
          {
            title: "Abonelikler",
            href: "/admin/subscriptions",
            icon: ShieldCheck,
            isActive: pathname.startsWith("/admin/subscriptions"),
          },
        ],
      };

      return [coreGroup, adminGroup];
    } else if (userRole === "owner") {
      // Salon yönetimi grubu
      const salonManagementItems: NavigationItem[] = [
        {
          title: "Hizmetler",
          href: "/dashboard/services",
          icon: Scissors,
          isActive: pathname === "/dashboard/services",
        },
        {
          title: "Personel",
          href: "/dashboard/staff",
          icon: Users,
          isActive: pathname === "/dashboard/staff",
        },
        {
          title: "İçerik Yönetimi",
          href: "/dashboard/content",
          icon: FileText,
          isActive: pathname === "/dashboard/content",
        },
      ];

      // Ürün yönetimi özelliği kontrolü
      if (features.hasProductManagement) {
        salonManagementItems.push({
          title: "Ürünler",
          href: "/dashboard/products",
          icon: ShoppingBag,
          isActive: pathname.startsWith("/dashboard/products"),
        });
      }

      const salonManagementGroup: NavigationGroup = {
        label: "Salon Yönetimi",
        items: salonManagementItems,
      };

      // İş operasyonları grubu - Finans özelliği kontrolü
      const businessOperationsItems: NavigationItem[] = [];

      // Finans özelliği kontrolü
      if (features.hasFinance) {
        businessOperationsItems.push({
          title: "Finans",
          href: "/dashboard/finance",
          icon: DollarSign,
          isActive: pathname.startsWith("/dashboard/finance"),
        });
      }

      // Analitik özelliği kontrolü
      if (features.hasAnalytics) {
        businessOperationsItems.push({
          title: "Analitik",
          href: "/dashboard/analytics",
          icon: PieChart,
          isActive: pathname.startsWith("/dashboard/analytics"),
        });
      }

      // İş operasyonları grubu - sadece öğe varsa ekle
      const businessOperationsGroup: NavigationGroup | null =
        businessOperationsItems.length > 0
          ? {
              label: "İş Operasyonları",
              items: businessOperationsItems,
            }
          : null;

      // Program yönetimi grubu
      const scheduleManagementGroup: NavigationGroup = {
        label: "Program Yönetimi",
        items: [
          {
            title: "Çalışma Saatleri",
            href: "/dashboard/working-hours",
            icon: Clock,
            isActive: pathname === "/dashboard/working-hours",
          },
          {
            title: "Tatil Günleri",
            href: "/dashboard/holidays",
            icon: CalendarDays,
            isActive: pathname === "/dashboard/holidays",
          },
        ],
      };

      // Sistem grubu
      const systemItems: NavigationItem[] = [
        {
          title: "Abonelik",
          href: "/dashboard/subscription",
          icon: CreditCard,
          isActive: pathname.startsWith("/dashboard/subscription"),
        },
        {
          title: "Ayarlar",
          href: "/dashboard/settings",
          icon: Settings,
          isActive: pathname === "/dashboard/settings",
        },
        {
          title: "Referans Programı",
          href: "/dashboard/referrals",
          icon: Gift,
          isActive: pathname === "/dashboard/referrals",
        },
      ];

      // Özel alan adı özelliği kontrolü
      if (features.hasCustomDomain) {
        systemItems.push({
          title: "Özel Alan Adı",
          href: "/dashboard/custom-domain",
          icon: Globe,
          isActive: pathname === "/dashboard/custom-domain",
        });
      }

      const systemGroup: NavigationGroup = {
        label: "Sistem",
        items: systemItems,
      };

      // Grupları birleştir - null olanları filtrele
      const groups = [coreGroup, salonManagementGroup];
      if (businessOperationsGroup) groups.push(businessOperationsGroup);
      groups.push(scheduleManagementGroup, systemGroup);

      return groups;
    } else if (userRole === "staff") {
      // Personel için kişisel grup
      const personalGroup: NavigationGroup = {
        label: "Kişisel",
        items: [
          {
            title: "Programım",
            href: "/dashboard/my-schedule",
            icon: Briefcase,
            isActive: pathname === "/dashboard/my-schedule",
          },
          {
            title: "Profil",
            href: "/dashboard/profile",
            icon: Settings,
            isActive: pathname === "/dashboard/profile",
          },
        ],
      };

      return [coreGroup, personalGroup];
    }

    // Default navigation for unknown role
    return [coreGroup];
  }, [pathname, userRole, isAdminUser, features, salon]);

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-between px-3 py-2">
          <SidebarMenu className="flex-1">
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link href="/dashboard">
                  <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <Command className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">SalonFlow</span>
                    <span className="truncate text-xs">
                      {salon
                        ? salon.name
                        : userRole === "owner"
                        ? "Salon Sahibi"
                        : userRole === "staff"
                        ? "Personel"
                        : "Kullanıcı"}
                    </span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
          <div className="flex items-center">
            <NotificationPanel />
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {navigationGroups.map((group, index) => (
          <SidebarGroup key={group.label} className={index > 0 ? "mt-4" : ""}>
            <SidebarGroupLabel>{group.label}</SidebarGroupLabel>
            <SidebarMenu>
              {group.items.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                    data-active={item.isActive}
                  >
                    <Link href={item.href}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarFallback className="rounded-lg">
                      {user?.email?.substring(0, 2).toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">
                      {user?.email?.split("@")[0] || "Kullanıcı"}
                    </span>
                    <span className="truncate text-xs">
                      {user?.email || ""}
                    </span>
                  </div>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                side={isMobile ? "bottom" : "right"}
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarFallback className="rounded-lg">
                        {user?.email?.substring(0, 2).toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-medium">
                        {user?.email?.split("@")[0] || "Kullanıcı"}
                      </span>
                      <span className="truncate text-xs">
                        {user?.email || ""}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Çıkış Yap
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
