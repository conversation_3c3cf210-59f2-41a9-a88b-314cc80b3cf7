// Database types for SalonFlow

// Salon type
export interface Salon {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  logo_url?: string;
  created_at: string;
  updated_at: string;
  owner_id: string;
  slug: string;
}

// Working hours type
export interface WorkingHours {
  id: string;
  salon_id: string;
  day_of_week: number; // 0 = Sunday, 6 = Saturday
  open_time: string;
  close_time: string;
  is_closed: boolean;
  created_at: string;
  updated_at: string;
}

// Holiday type
export interface Holiday {
  id: string;
  salon_id: string;
  date: string;
  description?: string;
  is_recurring?: boolean;
  created_at: string;
  updated_at: string;
}

// Barber type
export interface Barber {
  id: string;
  salon_id: string;
  name: string;
  email?: string;
  phone?: string;
  profile_image_url?: string;
  user_id?: string;
  invitation_token?: string;
  invitation_sent_at?: string;
  invitation_accepted_at?: string;
  created_at: string;
  updated_at: string;
}

// Barber working hours type
export interface BarberWorkingHours {
  id: string;
  barber_id: string;
  day_of_week: number;
  open_time: string;
  close_time: string;
  is_closed: boolean;
  lunch_start_time?: string;
  lunch_end_time?: string;
  has_lunch_break?: boolean;
  created_at: string;
  updated_at: string;
}

// Service type
export interface Service {
  id: string;
  salon_id: string;
  name: string;
  description: string | null;
  duration: number; // Duration in minutes
  price: number;
  sort_order: number; // Display order
  created_at: string;
  updated_at: string;
}

// Barber service type
export interface BarberService {
  id: string;
  barber_id: string;
  service_id: string;
  created_at: string;
}

// Customer type
export interface Customer {
  id: string;
  salon_id: string; // Added salon_id field
  name: string;
  surname: string;
  email?: string;
  phone: string;
  notes?: string; // Added notes field
  created_at: string;
  updated_at: string;
}

// Appointment type
export interface Appointment {
  id: string;
  salon_id: string;
  barber_id: string;
  customer_id?: string; // Now optional
  service_id: string;
  date: string;
  start_time: string;
  end_time: string;
  status: 'booked' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  fullname?: string; // New field
  phonenumber?: string; // New field
  email?: string; // New field
  created_at: string;
  updated_at: string;
  // Join fields from Supabase queries
  barbers?: {
    name: string;
  };
  services?: {
    name: string;
    duration: number;
  };
  customers?: {
    name: string;
    surname: string;
    phone: string;
    email?: string;
  };
}

// Subscription plan type
export interface SubscriptionPlan {
  id: string;
  name: string;
  price_monthly: number;
  price_yearly: number;
  max_staff: number;
  features: {
    analytics: boolean;
    finance: boolean;
    custom_domain: boolean;
    [key: string]: any;
  };
  created_at: string;
}

// Salon subscription type
export interface SalonSubscription {
  id: string;
  salon_id: string;
  plan_id: string;
  plan?: 'basic' | 'standard' | 'premium'; // Eski alan, geriye uyumluluk için
  status: 'trial' | 'active' | 'past_due' | 'suspended' | 'cancelled';
  start_date: string;
  end_date?: string;
  trial_end_date?: string;
  payment_method?: string;
  is_yearly: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Join fields
  plans?: SubscriptionPlan;
}

// Subscription payment type
export interface SubscriptionPayment {
  id: string;
  subscription_id: string;
  amount: number;
  original_amount?: number; // Yeni alan
  discount_amount?: number; // Yeni alan
  discount_type?: string; // Yeni alan
  discount_reference_id?: string; // Yeni alan
  period_start_date?: string; // Yeni alan
  period_end_date?: string; // Yeni alan
  payment_date: string;
  payment_method?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  invoice_number?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Referral code type
export interface ReferralCode {
  id: string;
  salon_id: string;
  code: string;
  uses: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Referral benefit type
export interface ReferralBenefit {
  id: string;
  referrer_salon_id: string;
  referred_salon_id: string;
  referral_code_id: string;
  benefit_type: string; // 'referred_discount' olarak güncellenecek
  benefit_value: string;
  discount_amount?: number; // Yeni alan
  discount_applied?: boolean; // Yeni alan
  discount_applied_payment_id?: string; // Yeni alan
  is_applied: boolean;
  applied_date?: string;
  created_at: string;
  updated_at: string;
}

// Pending referral type
export interface PendingReferral {
  id: string;
  user_id: string;
  referral_code: string;
  is_applied: boolean;
  created_at: string;
  updated_at: string;
}

// Finance Category type
export interface FinanceCategory {
  id: string;
  salon_id: string;
  name: string;
  type: 'income' | 'expense';
  description?: string;
  color?: string;
  is_system_default: boolean;
  created_at: string;
  updated_at: string;
}

// Finance Transaction type
export interface FinanceTransaction {
  id: string;
  salon_id: string;
  category_id: string;
  amount: number;
  transaction_date: string;
  description: string | null;
  appointment_id: string | null;
  service_id: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
  finance_categories?: {
    name: string;
    type: string;
    color: string;
  };
}

// Product type
export interface Product {
  id: string;
  salon_id: string;
  name: string;
  description?: string;
  price?: number;
  category?: string;
  image_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Product Category type
export interface ProductCategory {
  id: string;
  salon_id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

// Add new interface for service financial reports
export interface ServiceFinancialSummary {
  service_id: string;
  service_name: string;
  total_income: number;
  transaction_count: number;
}

// Salon Content type
export interface SalonContent {
  id: string;
  salon_id: string;
  section: 'hero' | 'about' | 'services' | 'contact';
  content_key: string;
  content_value?: string;
  content_type: 'text' | 'number' | 'boolean' | 'json';
  created_at: string;
  updated_at: string;
}

// Salon Testimonials type
export interface SalonTestimonial {
  id: string;
  salon_id: string;
  customer_name: string;
  rating: number; // 1-5
  comment: string;
  service_name?: string;
  date_text?: string; // e.g., "2 hafta önce"
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

// Database types for insert operations (without id and timestamps)
export type SalonInsert = Omit<Salon, 'id' | 'created_at' | 'updated_at'>;
export type WorkingHoursInsert = Omit<WorkingHours, 'id' | 'created_at' | 'updated_at'>;
export type HolidayInsert = Omit<Holiday, 'id' | 'created_at' | 'updated_at'>;
export type BarberInsert = Omit<Barber, 'id' | 'created_at' | 'updated_at'>;
export type BarberWorkingHoursInsert = Omit<BarberWorkingHours, 'id' | 'created_at' | 'updated_at'>;
export type ServiceInsert = Omit<Service, 'id' | 'created_at' | 'updated_at'> & { sort_order?: number };
export type BarberServiceInsert = Omit<BarberService, 'id' | 'created_at'>;
export type CustomerInsert = Omit<Customer, 'id' | 'created_at' | 'updated_at'>;
export type AppointmentInsert = Omit<Appointment, 'id' | 'created_at' | 'updated_at'>;
export type SubscriptionPlanInsert = Omit<SubscriptionPlan, 'id' | 'created_at'>;
export type SalonSubscriptionInsert = Omit<SalonSubscription, 'id' | 'created_at' | 'updated_at' | 'plans'>;
export type SubscriptionPaymentInsert = Omit<SubscriptionPayment, 'id' | 'created_at' | 'updated_at'>;
export type ReferralCodeInsert = Omit<ReferralCode, 'id' | 'created_at' | 'updated_at'>;
export type ReferralBenefitInsert = Omit<ReferralBenefit, 'id' | 'created_at' | 'updated_at'>;
export type PendingReferralInsert = Omit<PendingReferral, 'id' | 'created_at' | 'updated_at'>;
export type FinanceCategoryInsert = Omit<FinanceCategory, 'id' | 'created_at' | 'updated_at'>;
export type FinanceTransactionInsert = Omit<FinanceTransaction, 'id' | 'created_at' | 'updated_at'>;
export type ProductInsert = Omit<Product, 'id' | 'created_at' | 'updated_at'>;
export type ProductCategoryInsert = Omit<ProductCategory, 'id' | 'created_at' | 'updated_at'>;
export type SalonContentInsert = Omit<SalonContent, 'id' | 'created_at' | 'updated_at'>;
export type SalonTestimonialInsert = Omit<SalonTestimonial, 'id' | 'created_at' | 'updated_at'>;

// Database types for update operations (all fields optional except id)
export type SalonUpdate = Partial<Omit<Salon, 'id' | 'created_at' | 'updated_at' | 'owner_id'>> & { id: string };
export type WorkingHoursUpdate = Partial<Omit<WorkingHours, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type HolidayUpdate = Partial<Omit<Holiday, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type BarberUpdate = Partial<Omit<Barber, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type BarberWorkingHoursUpdate = Partial<Omit<BarberWorkingHours, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type ServiceUpdate = Partial<Omit<Service, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type CustomerUpdate = Partial<Omit<Customer, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type AppointmentUpdate = Partial<Omit<Appointment, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type SubscriptionPlanUpdate = Partial<Omit<SubscriptionPlan, 'id' | 'created_at'>> & { id: string };
export type SalonSubscriptionUpdate = Partial<Omit<SalonSubscription, 'id' | 'created_at' | 'updated_at' | 'plans'>> & { id: string };
export type SubscriptionPaymentUpdate = Partial<Omit<SubscriptionPayment, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type ReferralCodeUpdate = Partial<Omit<ReferralCode, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type ReferralBenefitUpdate = Partial<Omit<ReferralBenefit, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type FinanceCategoryUpdate = Partial<Omit<FinanceCategory, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type FinanceTransactionUpdate = Partial<Omit<FinanceTransaction, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type ProductUpdate = Partial<Omit<Product, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type ProductCategoryUpdate = Partial<Omit<ProductCategory, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type SalonContentUpdate = Partial<Omit<SalonContent, 'id' | 'created_at' | 'updated_at'>> & { id: string };
export type SalonTestimonialUpdate = Partial<Omit<SalonTestimonial, 'id' | 'created_at' | 'updated_at'>> & { id: string };
