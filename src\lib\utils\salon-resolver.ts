import { SupabaseClient } from "@supabase/supabase-js";
import { supabaseClient } from '@/lib/supabase-singleton';


const supabase = supabaseClient;


// Salon bilgilerini içeren statik mapping
interface SalonInfo {
  customDomain?: string;
  slug?: string;
}

// SalonId -> {customDomain, slug} şeklinde mapping
const SALON_MAPPING: Record<string, SalonInfo> = {
  // Her salon için bir entry
  "f057c575-a7df-4987-976d-0ad711ef5a76": {
    customDomain: "mustafabaytan.com",
    slug: "mustafabaytan",
  },
  // Yeni salonları buraya ekleyin
};

// Ters mapping için yardımcı objeler (performans için)
const DOMAIN_TO_SALON_ID: Record<string, string> = {};
const SLUG_TO_SALON_ID: Record<string, string> = {};

// Ters mapping'leri oluştur
Object.entries(SALON_MAPPING).forEach(([salonId, info]) => {
  if (info.customDomain) {
    DOMAIN_TO_SALON_ID[info.customDomain] = salonId;
  }
  if (info.slug) {
    SLUG_TO_SALON_ID[info.slug] = salonId;
  }
});

/**
 * Retrieves salon information for a given slug.
 * Checks static mapping first, then queries the database.
 * Returns the salon ID if found, null otherwise.
 */
export async function getSalonInfoFromSlug(
  slug: string,
  supabase: SupabaseClient
): Promise<{ salonId: string } | null> {
  try {
    // 1. Önce statik eşleştirmeleri kontrol et
    const salonId = SLUG_TO_SALON_ID[slug];
    if (salonId) {
      console.log(`Slug '${slug}' için statik eşleştirme bulundu: ${salonId}`);
      return { salonId };
    }

    // 2. Veritabanında ara
    console.log(`Slug '${slug}' için veritabanında arama yapılıyor`);
    const { data, error } = await supabase
      .from("salons")
      .select("id")
      .eq("slug", slug)
      .single();

    if (error) {
      if (error.code !== "PGRST116") {
        // PGRST116: Row not found, which is fine
        console.error("Error fetching salon by slug:", error.message);
      }
      return null;
    }

    if (data?.id) {
      return { salonId: data.id };
    }

    return null;
  } catch (error) {
    console.error("Error in getSalonInfoFromSlug:", error);
    return null;
  }
}

/**
 * Retrieves salon information for a given custom domain.
 * Checks static mapping first, then queries the database.
 */
export async function getSalonInfoForCustomDomain(
  hostname: string,
  supabase: SupabaseClient
): Promise<{ salonId: string } | null> {
  const domainWithoutWww = hostname.replace(/^www\./, "");

  // 1. Check static mapping
  const salonId = DOMAIN_TO_SALON_ID[domainWithoutWww];
  if (salonId) {
    console.log(
      `Domain '${domainWithoutWww}' için statik eşleştirme bulundu: ${salonId}`
    );
    return { salonId };
  }

  // 2. Check database for verified custom domain
  const { data: customDomainData, error } = await supabase
    .from("custom_domains")
    .select("salon_id")
    .eq("domain", domainWithoutWww)
    .eq("is_verified", true)
    .single();

  if (error && error.code !== "PGRST116") {
    // PGRST116: Row not found, which is fine
    console.error("Error fetching custom domain:", error.message);
    return null; // Or handle error more gracefully
  }

  if (customDomainData?.salon_id) {
    return { salonId: customDomainData.salon_id };
  }

  return null;
}

/**
 * Get salon slug by salon ID
 */
/**
 * Client tarafı için salon bilgilerini slug'dan getiren fonksiyon
 * Supabase client'ı otomatik olarak oluşturur
 */
export async function getSalonInfoFromSlugClient(
  slug: string
): Promise<{ id: string, name: string }> {

  try {
    // 1. Önce statik eşleştirmeleri kontrol et
    const salonId = SLUG_TO_SALON_ID[slug];
    if (salonId) {
      // Salon adını almak için veritabanına sorgu yap
      const { data, error } = await supabase
        .from("salons")
        .select("name")
        .eq("id", salonId)
        .single();

      if (!error && data) {
        return { id: salonId, name: data.name };
      }
    }

    // 2. Veritabanında ara
    const { data, error } = await supabase
      .from("salons")
      .select("id, name")
      .eq("slug", slug)
      .single();

    if (error) {
      console.error("Error fetching salon by slug:", error.message);
      throw new Error("Salon bulunamadı");
    }

    if (data) {
      return { id: data.id, name: data.name };
    }

    throw new Error("Salon bulunamadı");
  } catch (error) {
    console.error("Error in getSalonInfoFromSlugClient:", error);
    throw new Error("Salon bilgileri alınırken bir hata oluştu");
  }
}

export async function getSalonSlugById(
  salonId: string,
  supabase: SupabaseClient
): Promise<string | null> {
  try {
    // 1. Check static mapping first
    const salonInfo = SALON_MAPPING[salonId];
    if (salonInfo?.slug) {
      return salonInfo.slug;
    }

    // 2. Check database
    const { data, error } = await supabase
      .from("salons")
      .select("slug")
      .eq("id", salonId)
      .single();

    if (error) {
      if (error.code !== "PGRST116") {
        console.error("Error fetching salon slug:", error.message);
      }
      return null;
    }

    return data?.slug || null;
  } catch (error) {
    console.error("Error in getSalonSlugById:", error);
    return null;
  }
}
