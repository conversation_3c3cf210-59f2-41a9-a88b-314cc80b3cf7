/**
 * Test for time comparison fix in client booking form
 * Tests the scenario where endTime crosses midnight (00:00)
 */

const { format, addMinutes, parse, isAfter, isBefore, isWithinInterval } = require("date-fns")

console.log('=== Time Comparison Fix Tests ===\n')
const testDate = new Date('2024-01-15') // Monday

// Test 1: Midnight crossing
console.log('Test 1: Midnight crossing')
{
    // Test scenario: appointment from 23:30 to 00:30 (next day)
    const timeSlot = "23:30"
    const serviceDuration = 60 // 60 minutes
    const closeTime = "00:30" // Salon closes at 00:30 (next day)
    
    // Parse times using the same reference date
    const startTime = parse(timeSlot, "HH:mm", testDate)
    const endTime = addMinutes(startTime, serviceDuration)
    const closeTimeDate = parse(closeTime, "HH:mm", testDate)
    
    console.log('Start time:', format(startTime, "yyyy-MM-dd HH:mm"))
    console.log('End time:', format(endTime, "yyyy-MM-dd HH:mm"))
    console.log('Close time:', format(closeTimeDate, "yyyy-MM-dd HH:mm"))
    
    // With string comparison (old way - WRONG):
    const endTimeString = format(endTime, "HH:mm")
    const stringComparison = endTimeString > closeTime
    console.log('String comparison result (WRONG):', stringComparison)
    console.log('String values:', endTimeString, '>', closeTime, '=', stringComparison)
    
    // With Date comparison (new way - CORRECT):
    const dateComparison = isAfter(endTime, closeTimeDate)
    console.log('Date comparison result (CORRECT):', dateComparison)
    
    // The string comparison should be wrong (true) because "00:30" > "00:30" is false
    // but endTime is actually next day 00:30 which should be compared properly
    console.log('✓ String comparison should be false:', stringComparison === false)
    console.log('✓ Date comparison should be true:', dateComparison === true)
}

console.log('\nTest 2: Lunch break overlap')
{
    const timeSlot = "12:30"
    const serviceDuration = 60
    const lunchStart = "13:00"
    const lunchEnd = "14:00"
    
    const startTime = parse(timeSlot, "HH:mm", testDate)
    const endTime = addMinutes(startTime, serviceDuration)
    const lunchStartDate = parse(lunchStart, "HH:mm", testDate)
    const lunchEndDate = parse(lunchEnd, "HH:mm", testDate)
    
    // Check if appointment overlaps with lunch break
    const overlaps = isWithinInterval(startTime, { start: lunchStartDate, end: lunchEndDate }) ||
                    isWithinInterval(endTime, { start: lunchStartDate, end: lunchEndDate }) ||
                    (isBefore(startTime, lunchStartDate) && isAfter(endTime, lunchEndDate))
    
    console.log('Appointment: 12:30-13:30, Lunch: 13:00-14:00')
    console.log('Overlaps:', overlaps)
    
    expect(overlaps).toBe(true) // Should overlap
  })

  test('should handle existing appointment conflict correctly', () => {
    const timeSlot = "14:00"
    const serviceDuration = 60
    const existingStart = "14:30"
    const existingEnd = "15:30"
    
    const startTime = parse(timeSlot, "HH:mm", testDate)
    const endTime = addMinutes(startTime, serviceDuration)
    const existingStartDate = parse(existingStart, "HH:mm", testDate)
    const existingEndDate = parse(existingEnd, "HH:mm", testDate)
    
    // Check if new appointment conflicts with existing one
    const conflicts = isWithinInterval(startTime, { start: existingStartDate, end: existingEndDate }) ||
                     isWithinInterval(endTime, { start: existingStartDate, end: existingEndDate }) ||
                     (isBefore(startTime, existingStartDate) && isAfter(endTime, existingEndDate))
    
    console.log('New appointment: 14:00-15:00, Existing: 14:30-15:30')
    console.log('Conflicts:', conflicts)
    
    expect(conflicts).toBe(true) // Should conflict
  })

  test('should handle midnight crossing in existing appointments', () => {
    const timeSlot = "23:00"
    const serviceDuration = 120 // 2 hours
    const existingStart = "23:30"
    const existingEnd = "00:30" // Next day
    
    const startTime = parse(timeSlot, "HH:mm", testDate)
    const endTime = addMinutes(startTime, serviceDuration) // Will be 01:00 next day
    const existingStartDate = parse(existingStart, "HH:mm", testDate)
    
    // Handle midnight crossing for existing appointment
    let existingEndDate = parse(existingEnd, "HH:mm", testDate)
    if (existingEnd < existingStart) {
      existingEndDate = parse(existingEnd, "HH:mm", new Date(testDate.getTime() + 24 * 60 * 60 * 1000))
    }
    
    console.log('New appointment:', format(startTime, "HH:mm"), '-', format(endTime, "HH:mm"))
    console.log('Existing appointment:', format(existingStartDate, "HH:mm"), '-', format(existingEndDate, "HH:mm"))
    
    // Check conflict
    const conflicts = isWithinInterval(startTime, { start: existingStartDate, end: existingEndDate }) ||
                     isWithinInterval(endTime, { start: existingStartDate, end: existingEndDate }) ||
                     (isBefore(startTime, existingStartDate) && isAfter(endTime, existingEndDate))
    
    console.log('Conflicts:', conflicts)
    expect(conflicts).toBe(true) // Should conflict
  })
})
