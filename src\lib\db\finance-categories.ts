import { supabaseClient } from '../supabase-singleton';
import { FinanceCategory, FinanceCategoryInsert, FinanceCategoryUpdate } from './types';

/**
 * Get all finance categories for a salon
 */
export async function getFinanceCategories(salonId: string) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_categories')
    .select('*')
    .eq('salon_id', salonId)
    .order('type')
    .order('name');

  if (error) {
    console.error('Error fetching finance categories:', error);
    throw error;
  }

  return data as FinanceCategory[];
}

/**
 * Get finance categories by type for a salon
 */
export async function getFinanceCategoriesByType(salonId: string, type: 'income' | 'expense') {
    const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_categories')
    .select('*')
    .eq('salon_id', salonId)
    .eq('type', type)
    .order('name');

  if (error) {
    console.error('Error fetching finance categories by type:', error);
    throw error;
  }

  console.log(`Retrieved ${data ? data.length : 0} finance categories for type`);
  return data as FinanceCategory[];
}

/**
 * Get a finance category by ID
 */
export async function getFinanceCategoryById(id: string) {
    const supabase = supabaseClient;
  const { data, error } = await supabase
    .from('finance_categories')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching finance category by ID:', error);
    throw error;
  }

  return data as FinanceCategory;
}

/**
 * Create a new finance category
 */
export async function createFinanceCategory(category: FinanceCategoryInsert) {
    const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_categories')
    .insert(category)
    .select()
    .single();

  if (error) {
    console.error('Error creating finance category:', error);
    throw error;
  }

  return data as FinanceCategory;
}

/**
 * Update a finance category
 */
export async function updateFinanceCategory({ id, ...category }: FinanceCategoryUpdate) {
    const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_categories')
    .update(category)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating finance category:', error);
    throw error;
  }

  return data as FinanceCategory;
}

/**
 * Delete a finance category
 */
export async function deleteFinanceCategory(id: string) {
    const supabase = supabaseClient;

  const { error } = await supabase
    .from('finance_categories')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting finance category:', error);
    throw error;
  }

  return true;
}
