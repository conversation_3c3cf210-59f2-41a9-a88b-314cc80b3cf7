"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { format, addMinutes, parse } from "date-fns"
import * as z from "zod"
import { toast } from "sonner"
import { Search } from "lucide-react"
import { getSupabaseBrowser } from "@/lib/supabase"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { DatePicker } from "@/components/ui/date-picker"
import { TimeSlotGrid } from "@/components/ui/time-slot-grid"
import {
  appointments,
  barbers,
  services,
  barberWorkingHours,
  customers,
  holidays,
  Barber,
  Service,
  Customer,
} from "@/lib/db"
import { HolidayDate } from "@/components/ui/holiday-calendar"

// Custom hook for debouncing values
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Form schema
const formSchema = z.object({
  date: z.date({
    required_error: "Tarih seçmelisiniz.",
  }),
  start_time: z.string({
    required_error: "Başlangıç saati seçmelisiniz.",
  }),
  barber_id: z.string({
    required_error: "Berber seçmelisiniz.",
  }),
  service_id: z.string({
    required_error: "Hizmet seçmelisiniz.",
  }),
  fullname: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  phonenumber: z.string().min(10, {
    message: "Geçerli bir telefon numarası giriniz.",
  }),
  email: z.string().email({
    message: "Geçerli bir e-posta adresi giriniz.",
  }).optional().or(z.literal("")),
  notes: z.string().optional(),
})

interface AppointmentFormProps {
  salonId: string
  appointmentId?: string
  initialDate?: Date
  initialTime?: string
  initialBarberId?: string
  initialServiceId?: string
  onSuccess?: () => void
  onCancel?: () => void
}

export function AppointmentForm({
  salonId,
  appointmentId,
  initialDate,
  initialTime,
  initialBarberId,
  initialServiceId,
  onSuccess,
  onCancel
}: AppointmentFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [barbersList, setBarbersList] = useState<Barber[]>([])
  const [servicesList, setServicesList] = useState<Service[]>([])
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate || new Date())
  const [selectedBarber, setSelectedBarber] = useState<string | null>(null)
  const [appointmentStatus, setAppointmentStatus] = useState<'booked' | 'completed' | 'cancelled' | 'no-show'>('booked')

  // State variables for simplified flow
  const [disabledDates, setDisabledDates] = useState<Date[]>([])
  const [isDatePickerEnabled, setIsDatePickerEnabled] = useState(!!initialBarberId)
  const [isTimeSelectionEnabled, setIsTimeSelectionEnabled] = useState(!!appointmentId || (!!initialDate && !!initialBarberId))
  const [holidayDates, setHolidayDates] = useState<HolidayDate[]>([])
  const [barberWorkingHoursList, setBarberWorkingHoursList] = useState<any[]>([])
  const [existingAppointmentTimes, setExistingAppointmentTimes] = useState<string[]>([])

  // New state variables for optimization
  const [salonHolidays, setSalonHolidays] = useState<any[]>([])
  const [existingAppointments, setExistingAppointments] = useState<any[]>([])

  // Customer search state
  const [customerMode, setCustomerMode] = useState<"new" | "existing">("new")
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<Customer[]>([])
  const [isSearching, setIsSearching] = useState(false)

  // Debounced search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: initialDate,
      start_time: initialTime,
      barber_id: initialBarberId,
      service_id: initialServiceId,
      fullname: "",
      phonenumber: "",
      email: "",
      notes: "",
    },
  })

  // Client-side function to check if a date is a holiday
  const isHolidayDate = useCallback((date: Date) => {
    const formattedDate = format(date, "yyyy-MM-dd")
    return salonHolidays.some(holiday => format(new Date(holiday.date), "yyyy-MM-dd") === formattedDate)
  }, [salonHolidays])

  // Update disabled dates based on barber working hours and holidays
  const updateDisabledDates = useCallback(async (barberId: string) => {
    try {
      // Get barber working hours for all days
      const workingHours = await barberWorkingHours.getBarberWorkingHours(barberId)
      setBarberWorkingHoursList(workingHours)

      // Find days when the barber doesn't work
      const closedDays = []
      for (let i = 0; i < 7; i++) {
        const dayWorkingHours = workingHours.find(wh => wh.day_of_week === i)
        if (!dayWorkingHours || dayWorkingHours.is_closed) {
          closedDays.push(i)
        }
      }

      // Create disabled dates for the next 3 months
      const dates: Date[] = []
      const today = new Date()
      const threeMonthsLater = new Date()
      threeMonthsLater.setMonth(today.getMonth() + 3)

      // Convert holiday dates to Date objects for disabled dates
      const holidayDateObjects = salonHolidays.map(holiday => new Date(holiday.date))

      // Convert to HolidayDate format for the TimeSlotGrid component
      const holidayDateFormatted = salonHolidays.map(holiday => ({
        date: new Date(holiday.date),
        description: holiday.description || "Tatil Günü"
      }))

      setHolidayDates(holidayDateFormatted)

      const currentDate = new Date(today)
      while (currentDate <= threeMonthsLater) {
        const dayOfWeek = currentDate.getDay()
        const currentDateStr = format(currentDate, "yyyy-MM-dd")

        // If the barber doesn't work on this day of the week, add it to disabled dates
        if (closedDays.includes(dayOfWeek)) {
          // Create a new Date object to avoid reference issues
          dates.push(new Date(currentDate.getTime()))
        }
        // Check if the date is a holiday
        else if (holidayDateObjects.some(date => format(date, "yyyy-MM-dd") === currentDateStr)) {
          dates.push(new Date(currentDate.getTime()))
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1)
      }

      setDisabledDates(dates)
    } catch (error) {
      console.error("Error updating disabled dates:", error)
      // Default to weekends if error
      const dates: Date[] = []
      const today = new Date()
      const threeMonthsLater = new Date()
      threeMonthsLater.setMonth(today.getMonth() + 3)

      const currentDate = new Date(today)
      while (currentDate <= threeMonthsLater) {
        const dayOfWeek = currentDate.getDay()
        if (dayOfWeek === 0) { // Sunday
          dates.push(new Date(currentDate.getTime()))
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1)
      }

      setDisabledDates(dates)
    }
  }, [salonHolidays])

  useEffect(() => {
    // In edit mode (appointmentId exists), always keep time selection enabled
    // Otherwise, enable it only when all required fields are selected
    if (appointmentId || (selectedBarber && selectedDate && selectedService)) {
      setIsTimeSelectionEnabled(true);
    }
  }, [appointmentId, selectedBarber, selectedDate, selectedService]);

  // Client-side function to calculate available times
  const calculateAvailableTimes = useCallback((date: Date, barberId: string, service: Service | null = null, appointmentsToUse = existingAppointments) => {
    try {
      const formattedDate = format(date, "yyyy-MM-dd")
      const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday

      // Check if the selected date is a holiday
      if (isHolidayDate(date)) {
        toast.error("Seçilen tarih tatil günüdür. Lütfen başka bir tarih seçin.")
        return []
      }

      // Get barber working hours for the specific day
      const workingHoursData = barberWorkingHoursList.find(wh => wh.day_of_week === dayOfWeek)

      // If barber doesn't work on this day or working hours not defined, return empty array
      if (!workingHoursData || workingHoursData.is_closed) {
        return []
      }

      // Filter existing appointments for the selected date and barber
      const filteredAppointments = appointmentsToUse.filter(apt =>
        apt.barber_id === barberId &&
        apt.date === formattedDate &&
        apt.status !== 'cancelled'
      )

      // If editing, exclude the current appointment from conflicts
      let appointmentsToCheck = filteredAppointments
      if (appointmentId) {
        // In edit mode, we need to exclude the current appointment from conflicts
        // We don't have the appointment ID in the query results, so we'll check by time
        const currentAppointmentTime = form.getValues("start_time")

        appointmentsToCheck = filteredAppointments.filter(apt => {
          return apt.start_time.substring(0, 5) !== currentAppointmentTime
        })
      }

      // Set existing appointment times for the TimeSlotGrid component
      const existingTimes = appointmentsToCheck.map(apt => apt.start_time.substring(0, 5))

      // In edit mode, make sure we don't include the current appointment time in existingAppointmentTimes
      if (appointmentId) {
        const currentAppointmentTime = form.getValues("start_time")
        const filteredExistingTimes = existingTimes.filter(time => time !== currentAppointmentTime)
        setExistingAppointmentTimes(filteredExistingTimes)
      } else {
        setExistingAppointmentTimes(existingTimes)
      }

      // Create time slots based on working hours
      const timeSlots = []

      // Parse working hours
      const openTime = workingHoursData.open_time.substring(0, 5)
      const closeTime = workingHoursData.close_time.substring(0, 5)

      const [openHour, openMinute] = openTime.split(':').map(Number)
      const [closeHour, closeMinute] = closeTime.split(':').map(Number)

      // Check if the barber has a lunch break
      const hasLunchBreak = workingHoursData.has_lunch_break || false
      let lunchStartHour = 0
      let lunchStartMinute = 0
      let lunchEndHour = 0
      let lunchEndMinute = 0

      if (hasLunchBreak &&
          workingHoursData.lunch_start_time &&
          workingHoursData.lunch_end_time) {
        const lunchStartTime = workingHoursData.lunch_start_time.substring(0, 5)
        const lunchEndTime = workingHoursData.lunch_end_time.substring(0, 5)

        const lunchStartParts = lunchStartTime.split(':').map(Number)
        const lunchEndParts = lunchEndTime.split(':').map(Number)

        lunchStartHour = lunchStartParts[0]
        lunchStartMinute = lunchStartParts[1]
        lunchEndHour = lunchEndParts[0]
        lunchEndMinute = lunchEndParts[1]
      }

      // Create 30-minute time slots from opening time
      let currentHour = openHour
      let currentMinute = openMinute

      // Create time slots until 30 minutes before closing time (for service duration)
      while (
        currentHour < closeHour ||
        (currentHour === closeHour && currentMinute < closeMinute - 30)
      ) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`

        // Skip lunch break times
        const isLunchBreak = hasLunchBreak && (
          (currentHour > lunchStartHour || (currentHour === lunchStartHour && currentMinute >= lunchStartMinute)) &&
          (currentHour < lunchEndHour || (currentHour === lunchEndHour && currentMinute < lunchEndMinute))
        )

        if (!isLunchBreak) {
          timeSlots.push(timeString)
        }

        // Add 30 minutes
        currentMinute += 30
        if (currentMinute >= 60) {
          currentHour += 1
          currentMinute = 0
        }
      }

      // Use the provided service or fall back to selectedService
      const serviceToUse = service || selectedService

      // If no service is selected or we're in edit mode, return all time slots without filtering for conflicts
      if (!serviceToUse || appointmentId) {


        // If we're in edit mode, we still need to filter out times that conflict with other appointments
        if (appointmentId) {


          const availableSlots = timeSlots.filter(timeSlot => {
            // Skip lunch break times
            const timeHour = parseInt(timeSlot.split(':')[0], 10)
            const timeMinute = parseInt(timeSlot.split(':')[1], 10)

            const isLunchBreak = hasLunchBreak && (
              (timeHour > lunchStartHour || (timeHour === lunchStartHour && timeMinute >= lunchStartMinute)) &&
              (timeHour < lunchEndHour || (timeHour === lunchEndHour && timeMinute < lunchEndMinute))
            )

            if (isLunchBreak) {

              return false
            }

            // Skip times that exceed working hours
            if (timeSlot < openTime || timeSlot > closeTime) {

              return false
            }

            // In edit mode, we only filter out times that conflict with OTHER appointments
            // We don't filter out the current appointment time
            const startTime = parse(timeSlot, "HH:mm", new Date())

            if (!serviceToUse) {

              return true // If no service selected, don't check duration
            }

            const endTime = addMinutes(startTime, serviceToUse.duration)
            const endTimeString = format(endTime, "HH:mm")

            // Skip this time slot if service duration exceeds working hours
            if (endTimeString > closeTime) {

              return false
            }

            // Check if this time slot conflicts with any existing appointment
            const hasConflict = appointmentsToCheck.some(apt => {
              const aptStartTime = apt.start_time.substring(0, 5)
              const aptEndTime = apt.end_time.substring(0, 5)

              // Check if the new appointment would overlap with an existing one
              const conflict = (
                (timeSlot >= aptStartTime && timeSlot < aptEndTime) || // Start time is during an existing appointment
                (endTimeString > aptStartTime && endTimeString <= aptEndTime) || // End time is during an existing appointment
                (timeSlot <= aptStartTime && endTimeString >= aptEndTime) // New appointment completely contains an existing one
              )



              return conflict
            })



            return !hasConflict
          })


          return availableSlots
        }


        return timeSlots
      }

      // Filter out times that conflict with existing appointments
      const availableSlots = timeSlots.filter(timeSlot => {
        const startTime = parse(timeSlot, "HH:mm", new Date())
        const endTime = addMinutes(startTime, serviceToUse.duration)
        const endTimeString = format(endTime, "HH:mm")

        // Skip this time slot if service duration exceeds working hours
        if (endTimeString > closeTime) return false

        // Check if this time slot conflicts with any existing appointment
        return !appointmentsToCheck.some(apt => {
          const aptStartTime = apt.start_time.substring(0, 5)
          const aptEndTime = apt.end_time.substring(0, 5)

          // Check if the new appointment would overlap with an existing one
          return (
            (timeSlot >= aptStartTime && timeSlot < aptEndTime) || // Start time is during an existing appointment
            (endTimeString > aptStartTime && endTimeString <= aptEndTime) || // End time is during an existing appointment
            (timeSlot <= aptStartTime && endTimeString >= aptEndTime) // New appointment completely contains an existing one
          )
        })
      })

      return availableSlots
    } catch (error) {
      console.error("Error calculating available times:", error)
      toast.error("Müsait saatler hesaplanırken bir hata oluştu.")
      return []
    }
  }, [appointmentId, barberWorkingHoursList, existingAppointments, form, isHolidayDate, selectedService, setExistingAppointmentTimes])

  // Load available times for a specific date and barber
  const loadAvailableTimes = useCallback(async (date: Date, barberId: string, service: Service | null = null) => {
    try {
      const formattedDate = format(date, "yyyy-MM-dd")

      // Auth gerektiren işlemler için getSupabaseBrowser kullan
      const supabase = getSupabaseBrowser()

      // Get all appointments for the barber on the selected date if not already loaded
      let query = supabase
        .from('appointments')
        .select('id, start_time, end_time, barber_id, date, status')
        .eq('barber_id', barberId)
        .eq('date', formattedDate)
        .not('status', 'eq', 'cancelled')

      // If editing, exclude the current appointment from the query
      if (appointmentId) {
        query = query.neq('id', appointmentId)
      }

      const { data: appointments, error } = await query

      if (error) throw error

      // Update existing appointments
      setExistingAppointments(appointments || [])

      // Calculate available times using client-side function with the fresh appointments data
      const availableSlots = calculateAvailableTimes(date, barberId, service, appointments || [])

      // If we're in edit mode and editing an existing appointment
      if (appointmentId) {
        // In edit mode, we need to make sure the original time is included in available slots
        // We'll get the current time from the form
        const currentTime = form.getValues("start_time")

        if (currentTime && !availableSlots.includes(currentTime)) {
          const updatedSlots = [currentTime, ...availableSlots]
          setAvailableTimes(updatedSlots)
          return updatedSlots
        }
      }

     //log available slots
      console.log("Available slots:", availableSlots)



      // For new appointments or if the original time is already in available slots
      setAvailableTimes(availableSlots)
      return availableSlots
    } catch (error) {
      console.error("Error loading available times:", error)
      toast.error("Müsait saatler yüklenirken bir hata oluştu.")
      return []
    }
  }, [calculateAvailableTimes, appointmentId, form])


  //log available slots
  useEffect(() => {
    console.log("Available times:", availableTimes)
  }, [availableTimes])


  // Load data
  useEffect(() => {
    async function loadData() {
      try {
        // Load salon holidays once
        const today = new Date()
        const threeMonthsLater = new Date()
        threeMonthsLater.setMonth(today.getMonth() + 3)

        const holidaysList = await holidays.getHolidaysInRange(
          salonId,
          format(today, "yyyy-MM-dd"),
          format(threeMonthsLater, "yyyy-MM-dd")
        )
        setSalonHolidays(holidaysList)

        // Load barbers
        const barbersData = await barbers.getBarbers(salonId)
        setBarbersList(barbersData)

        // Load services
        const servicesData = await services.getServices(salonId)
        setServicesList(servicesData)

        // If editing an existing appointment, load its data
        if (appointmentId) {
          const appointmentData = await appointments.getAppointmentById(appointmentId)

          if (appointmentData) {
            // Set appointment status
            setAppointmentStatus(appointmentData.status)

            // 1. Set barber first
            const barberId = appointmentData.barber_id
            setSelectedBarber(barberId)
            form.setValue("barber_id", barberId)
            setIsDatePickerEnabled(true)

            // Update disabled dates
            await updateDisabledDates(barberId)

            // 2. Set service
            const serviceId = appointmentData.service_id
            const service = servicesData.find(s => s.id === serviceId)
            setSelectedService(service || null)
            form.setValue("service_id", serviceId)

            // 3. Set date
            const appointmentDate = new Date(appointmentData.date)
            setSelectedDate(appointmentDate)
            form.setValue("date", appointmentDate)
            setIsTimeSelectionEnabled(true)

            // 4. Load available times and set time
            // For edit mode, we'll set the original time first to ensure it's used in calculations
            const startTime = appointmentData.start_time.substring(0, 5)
            form.setValue("start_time", startTime)



            // In edit mode, we'll generate all possible time slots based on working hours
            // and mark them all as available
            const dayOfWeek = appointmentDate.getDay()
            const workingHoursData = await barberWorkingHours.getBarberWorkingHours(barberId)
            const workingHoursForDay = workingHoursData.find(wh => wh.day_of_week === dayOfWeek)

            if (workingHoursForDay && !workingHoursForDay.is_closed) {
              const openTime = workingHoursForDay.open_time.substring(0, 5)
              const closeTime = workingHoursForDay.close_time.substring(0, 5)

              const [openHour, openMinute] = openTime.split(':').map(Number)
              const [closeHour, closeMinute] = closeTime.split(':').map(Number)

              // Generate all possible time slots
              const allTimeSlots = []
              let currentHour = openHour
              let currentMinute = openMinute

              // Check if the barber has a lunch break
              const hasLunchBreak = workingHoursForDay.has_lunch_break || false
              let lunchStartHour = 0
              let lunchStartMinute = 0
              let lunchEndHour = 0
              let lunchEndMinute = 0

              if (hasLunchBreak &&
                  workingHoursForDay.lunch_start_time &&
                  workingHoursForDay.lunch_end_time) {
                const lunchStartTime = workingHoursForDay.lunch_start_time.substring(0, 5)
                const lunchEndTime = workingHoursForDay.lunch_end_time.substring(0, 5)

                const lunchStartParts = lunchStartTime.split(':').map(Number)
                const lunchEndParts = lunchEndTime.split(':').map(Number)

                lunchStartHour = lunchStartParts[0]
                lunchStartMinute = lunchStartParts[1]
                lunchEndHour = lunchEndParts[0]
                lunchEndMinute = lunchEndParts[1]
              }

              while (
                currentHour < closeHour ||
                (currentHour === closeHour && currentMinute < closeMinute - 30)
              ) {
                const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`

                // Skip lunch break times
                const isLunchBreak = hasLunchBreak && (
                  (currentHour > lunchStartHour || (currentHour === lunchStartHour && currentMinute >= lunchStartMinute)) &&
                  (currentHour < lunchEndHour || (currentHour === lunchEndHour && currentMinute < lunchEndMinute))
                )

                if (!isLunchBreak) {
                  allTimeSlots.push(timeString)
                }

                // Add 30 minutes
                currentMinute += 30
                if (currentMinute >= 60) {
                  currentHour += 1
                  currentMinute = 0
                }
              }

              // Get existing appointments for the selected date and barber
              const supabase = getSupabaseBrowser()
              const { data: existingAppointmentsData } = await supabase
                .from('appointments')
                .select('id, start_time, end_time, service_id')
                .eq('barber_id', barberId)
                .eq('date', format(appointmentDate, "yyyy-MM-dd"))
                .neq('id', appointmentId)
                .not('status', 'eq', 'cancelled')



              // Filter out times that conflict with existing appointments
              let availableTimeSlots = [...allTimeSlots]

              if (existingAppointmentsData && existingAppointmentsData.length > 0 && service) {
                availableTimeSlots = allTimeSlots.filter(timeSlot => {
                  const startTimeObj = parse(timeSlot, "HH:mm", new Date())
                  const endTimeObj = addMinutes(startTimeObj, service.duration)
                  const endTimeString = format(endTimeObj, "HH:mm")

                  // Check if this time slot conflicts with any existing appointment
                  return !existingAppointmentsData.some((apt: { start_time: string, end_time: string }) => {
                    const aptStartTime = apt.start_time.substring(0, 5)
                    const aptEndTime = apt.end_time.substring(0, 5)

                    // Check if the new appointment would overlap with an existing one
                    return (
                      (timeSlot >= aptStartTime && timeSlot < aptEndTime) || // Start time is during an existing appointment
                      (endTimeString > aptStartTime && endTimeString <= aptEndTime) || // End time is during an existing appointment
                      (timeSlot <= aptStartTime && endTimeString >= aptEndTime) // New appointment completely contains an existing one
                    )
                  })
                })
              }



              // Make sure the original time is at the beginning
              if (availableTimeSlots.includes(startTime)) {
                availableTimeSlots.splice(availableTimeSlots.indexOf(startTime), 1)
              }

              const availableSlots = [startTime, ...availableTimeSlots]


              // Set all time slots as available
              setAvailableTimes(availableSlots)
            } else {
              // If the barber doesn't work on this day, just set the original time as available
              setAvailableTimes([startTime])
            }

            // 5. Set customer information
            form.setValue("fullname", appointmentData.fullname || "")
            form.setValue("phonenumber", appointmentData.phonenumber || "")
            form.setValue("email", appointmentData.email || "")
            form.setValue("notes", appointmentData.notes || "")
          }
        } else {
          // For new appointments with pre-selected values

          // 1. Set initial barber if provided or default to first barber
          const barberId = initialBarberId

          if (barberId) {
            setSelectedBarber(barberId)
            form.setValue("barber_id", barberId)
            setIsDatePickerEnabled(true)

            // Update disabled dates
            await updateDisabledDates(barberId)
          }

          // 2. Set initial service if provided or default to first service
          const serviceId = initialServiceId

          if (serviceId) {
            const service = servicesData.find(s => s.id === serviceId)
            setSelectedService(service || null)
            form.setValue("service_id", serviceId)
          }

          // 3. Set initial date if provided and barber is selected
          if (initialDate && barberId) {
            setSelectedDate(initialDate)
            form.setValue("date", initialDate)

            // 4. Load available times and set initial time if provided
            const service = serviceId ? servicesData.find(s => s.id === serviceId) || null : null
            const availableSlots = await loadAvailableTimes(initialDate, barberId, service)

            if (initialTime) {
              // Make sure the time is in the correct format (HH:MM)
              const formattedTime = initialTime.substring(0, 5)

              // Check if the time is available
              if (availableSlots.includes(formattedTime)) {
                form.setValue("start_time", formattedTime)
              } else if (availableSlots.length > 0) {
                // If not available, set the first available time and show notification
                form.setValue("start_time", availableSlots[0])
                toast.info("Seçilen saat müsait değil. İlk müsait saat otomatik olarak seçildi.")
              }
            } else if (availableSlots.length > 0) {
              // If no initial time provided, set the first available time
              form.setValue("start_time", availableSlots[0])
            }
          }
        }
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Veriler yüklenirken bir hata oluştu.")
      }
    }

    loadData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salonId, appointmentId, form, initialBarberId, initialServiceId, initialDate, initialTime])

  // Effect for debounced search
  useEffect(() => {
    if (debouncedSearchQuery && debouncedSearchQuery.length >= 2) {
      searchCustomers(debouncedSearchQuery)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchQuery])

  // Calculate end time based on start time and service duration
  const calculateEndTime = (startTime: string) => {
    // Return empty string if service not selected
    if (!selectedService) return ""

    // Return empty string if startTime is empty or invalid
    if (!startTime || typeof startTime !== 'string' || !startTime.includes(':')) return ""

    // Get hour and minute values
    const parts = startTime.split(":")
    if (parts.length !== 2) return "" // Invalid format

    const hours = parseInt(parts[0], 10)
    const minutes = parseInt(parts[1], 10)

    // Return empty string if hour or minute values are not valid numbers
    if (isNaN(hours) || isNaN(minutes)) return ""
    // Hour should be 0-23, minute should be 0-59
    if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) return ""

    try {
      // Create a valid date
      const startDate = new Date()
      startDate.setHours(hours, minutes, 0, 0)

      // Add service duration
      const endDate = addMinutes(startDate, selectedService.duration)

      // Format end time
      return format(endDate, "HH:mm")
    } catch (error) {
      console.error("Error calculating end time:", error)
      return ""
    }
  }

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)

    try {
      // Check if there are available times
      if (availableTimes.length === 0) {
        toast.error("Seçilen tarih ve berber için müsait saat bulunmamaktadır. Lütfen başka bir tarih veya berber seçin.")
        setIsLoading(false)
        return
      }

      // Check if start_time is valid
      if (!values.start_time || typeof values.start_time !== 'string' || !values.start_time.includes(':')) {
        toast.error("Geçerli bir başlangıç saati giriniz.")
        setIsLoading(false)
        return
      }

      const formattedDate = format(values.date, "yyyy-MM-dd")
      const endTime = calculateEndTime(values.start_time)

      if (!endTime) {
        toast.error("Bitiş saati hesaplanamadı. Lütfen bir hizmet seçin ve geçerli bir başlangıç saati girin.")
        setIsLoading(false)
        return
      }

      // Validate that the selected time is available
      // In edit mode, we allow the original time even if it's not in the available times list
      if (!appointmentId && !availableTimes.includes(values.start_time)) {
        toast.error("Seçilen saat müsait değil. Lütfen listeden bir saat seçin.")
        setIsLoading(false)
        return
      }

      // Prepare appointment data
      const appointmentData = {
        salon_id: salonId,
        date: formattedDate,
        start_time: values.start_time,
        end_time: endTime,
        barber_id: values.barber_id,
        service_id: values.service_id,
        fullname: values.fullname,
        phonenumber: values.phonenumber,
        email: values.email || undefined,
        status: "booked" as const,
        notes: values.notes,
      }

      if (appointmentId) {
        // Update existing appointment
        await appointments.updateAppointment({
          id: appointmentId,
          ...appointmentData
        })
        toast.success("Randevu başarıyla güncellendi.")
      } else {
        // Create new appointment
        await appointments.createAppointment(appointmentData)
        toast.success("Randevu başarıyla oluşturuldu.")
      }

      // Reset form if not editing
      if (!appointmentId) {
        form.reset({
          date: new Date(),
          start_time: "09:00",
          barber_id: "",
          service_id: "",
          fullname: "",
          phonenumber: "",
          email: "",
          notes: "",
        })
        setSelectedService(null)
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      } else {
        router.push("/dashboard/appointments")
        router.refresh()
      }
    } catch (error) {
      console.error("Error saving appointment:", error)
      toast.error("Randevu kaydedilirken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  // Search for customers by name or phone
  const searchCustomers = useCallback(async (query: string) => {
    if (!query || query.length < 2 || !salonId) return

    setIsSearching(true)
    try {
      const results = await customers.searchCustomers(salonId, query)
      setSearchResults(results)
    } catch (error) {
      console.error("Error searching customers:", error)
      toast.error("Müşteri arama sırasında bir hata oluştu.")
    } finally {
      setIsSearching(false)
    }
  }, [salonId])

  // Handle customer selection
  const handleCustomerSelect = useCallback((customer: Customer) => {
    form.setValue("fullname", `${customer.name} ${customer.surname}`)
    form.setValue("phonenumber", customer.phone)
    form.setValue("email", customer.email || "")
    setCustomerMode("new") // Switch back to form view after selection
  }, [form])


  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
        <div className="grid grid-cols-1 gap-4 sm:gap-6">
          {/* Randevu Bilgileri */}
          {appointmentStatus !== 'booked' && (
            <div className={`p-3 sm:p-4 rounded-lg border mb-3 sm:mb-4 ${
              appointmentStatus === 'completed' ? 'bg-green-100 border-green-300 dark:bg-green-900/30 dark:border-green-700' :
              appointmentStatus === 'cancelled' ? 'bg-red-100 border-red-300 dark:bg-red-900/30 dark:border-red-700' :
              'bg-amber-100 border-amber-300 dark:bg-amber-900/30 dark:border-amber-700'
            }`}>
              <h3 className="text-sm font-medium mb-1">Randevu Durumu: {
                appointmentStatus === 'completed' ? 'Tamamlandı' :
                appointmentStatus === 'cancelled' ? 'İptal Edildi' :
                'Gelmedi'
              }</h3>
              <p className="text-xs">
                Bu randevunun tarih ve saat bilgileri değiştirilemez.
              </p>
            </div>
          )}
          {/* Randevu Bilgileri ve Saat Seçimi - Yatay Düzende */}
          <div className="bg-muted/40 p-3 sm:p-4 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
              {/* Sol Taraf - Randevu Bilgileri */}
              <div>
                <h3 className="text-sm font-medium mb-2 sm:mb-3">Randevu Bilgileri</h3>
                <div className="space-y-3">
                  <FormField
                    control={form.control}
                    name="barber_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Berber</FormLabel>
                        <Select
                          onValueChange={async (barberId) => {
                            setSelectedBarber(barberId);
                            field.onChange(barberId);

                            // Enable date picker
                            setIsDatePickerEnabled(true);

                            // Update disabled dates
                            await updateDisabledDates(barberId);

                            // Reset time selection
                            form.setValue("start_time", "");
                            setAvailableTimes([]);
                            setIsTimeSelectionEnabled(false);

                            // If date is already selected, load available times
                            if (selectedDate) {
                              try {
                                // Get the current service
                                const serviceId = form.getValues("service_id");
                                const service = serviceId ? servicesList.find(s => s.id === serviceId) || null : null;

                                // Load available times with the new barber
                                const availableSlots = await loadAvailableTimes(selectedDate, barberId, service);

                                // If there are available slots, select the first one
                                if (availableSlots && availableSlots.length > 0) {
                                  form.setValue("start_time", availableSlots[0]);
                                  setIsTimeSelectionEnabled(true);
                                } else {
                                  // If we're in edit mode, always enable time selection
                                  if (appointmentId) {
                                    setIsTimeSelectionEnabled(true);
                                  } else {
                                    setIsTimeSelectionEnabled(false);
                                    toast.info("Seçilen tarih ve berber için müsait saat bulunamadı.");
                                  }
                                }
                              } catch (error) {
                                console.error("Error loading available times:", error);
                                toast.error("Müsait saatler yüklenirken bir hata oluştu.");

                                // If we're in edit mode, always enable time selection
                                if (appointmentId) {
                                  setIsTimeSelectionEnabled(true);
                                }
                              }
                            }
                          }}
                          value={field.value}
                          disabled={appointmentStatus !== 'booked'}
                        >
                          <FormControl>
                            <SelectTrigger className="h-9 text-sm w-full">
                              <SelectValue placeholder="Berber seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {barbersList.map((barber) => (
                              <SelectItem key={barber.id} value={barber.id} className="text-sm">
                                {barber.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="service_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Hizmet</FormLabel>
                        <Select
                          onValueChange={async (serviceId) => {
                            const service = servicesList.find(s => s.id === serviceId);
                            setSelectedService(service || null);
                            field.onChange(serviceId);

                            // If both date and barber are selected, update available times
                            if (selectedDate && selectedBarber) {
                              try {
                                // Load available times with the new service
                                const availableSlots = await loadAvailableTimes(selectedDate, selectedBarber, service);

                                // If there are available slots, select the first one
                                if (availableSlots && availableSlots.length > 0) {
                                  // Get the current time
                                  const currentTime = form.getValues("start_time");

                                  // If current time is not available, reset it and show notification
                                  if (currentTime && !availableSlots.includes(currentTime)) {
                                    form.setValue("start_time", availableSlots[0]);
                                    toast.info("Seçilen hizmet için bu saat müsait değil. İlk müsait saat otomatik olarak seçildi.");
                                  } else if (!currentTime) {
                                    // If no time is selected, select the first available time
                                    form.setValue("start_time", availableSlots[0]);
                                  }

                                  setIsTimeSelectionEnabled(true);
                                } else {
                                  // If we're in edit mode, always enable time selection
                                  if (appointmentId) {
                                    setIsTimeSelectionEnabled(true);
                                  } else {
                                    setIsTimeSelectionEnabled(false);
                                    form.setValue("start_time", "");
                                  }
                                }
                              } catch (error) {
                                console.error("Error loading available times:", error);
                                toast.error("Müsait saatler yüklenirken bir hata oluştu.");

                                // If we're in edit mode, always enable time selection
                                if (appointmentId) {
                                  setIsTimeSelectionEnabled(true);
                                }
                              }
                            }
                          }}
                          value={field.value}
                          disabled={appointmentStatus !== 'booked'}
                        >
                          <FormControl>
                            <SelectTrigger className="h-9 text-sm w-full">
                              <SelectValue placeholder="Hizmet seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {servicesList.map((service) => (
                              <SelectItem key={service.id} value={service.id} className="text-sm">
                                {service.name} ({service.duration} dk)
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Tarih</FormLabel>
                        <DatePicker
                          date={field.value}
                          setDate={async (date) => {
                            if (!date) return;

                            setSelectedDate(date);
                            field.onChange(date);

                            // Reset time selection
                            form.setValue("start_time", "");
                            setAvailableTimes([]);
                            setIsTimeSelectionEnabled(false);

                            // Load available times when both date and barber are selected
                            if (date && selectedBarber) {
                              try {
                                // Get the current service
                                const serviceId = form.getValues("service_id");
                                const service = serviceId ? servicesList.find(s => s.id === serviceId) || null : null;

                                // Load available times with the new date
                                const availableSlots = await loadAvailableTimes(date, selectedBarber, service);

                                // If there are available slots, select the first one
                                if (availableSlots && availableSlots.length > 0) {
                                  form.setValue("start_time", availableSlots[0]);
                                  setIsTimeSelectionEnabled(true);
                                } else {
                                  // If we're in edit mode, always enable time selection
                                  if (appointmentId) {
                                    setIsTimeSelectionEnabled(true);
                                  } else {
                                    setIsTimeSelectionEnabled(false);
                                    toast.info("Seçilen tarih ve berber için müsait saat bulunamadı.");
                                  }
                                }
                              } catch (error) {
                                console.error("Error loading available times:", error);
                                toast.error("Müsait saatler yüklenirken bir hata oluştu.");

                                // If we're in edit mode, always enable time selection
                                if (appointmentId) {
                                  setIsTimeSelectionEnabled(true);
                                }
                              }
                            }
                          }}
                          disabled={!isDatePickerEnabled || appointmentStatus !== 'booked'}
                          disabledDates={disabledDates}
                          fromDate={new Date()} // Disable past dates
                          holidayDates={holidayDates}
                          showHolidays={true}
                          compact={true} // Kompakt görünüm kullan
                        />
                        {appointmentStatus !== 'booked' && (
                          <div className="text-xs text-amber-500 mt-1">
                            {appointmentStatus === 'completed' && "Tamamlanmış randevuların tarihi değiştirilemez."}
                            {appointmentStatus === 'cancelled' && "İptal edilmiş randevuların tarihi değiştirilemez."}
                            {appointmentStatus === 'no-show' && "Gelmemiş randevuların tarihi değiştirilemez."}
                          </div>
                        )}
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Sağ Taraf - Saat Seçimi */}
              <div>
                <h3 className="text-sm font-medium mb-2 sm:mb-3">Saat Seçimi</h3>
                <FormField
                  control={form.control}
                  name="start_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Başlangıç Saati</FormLabel>
                      <FormControl>
                        {appointmentStatus !== 'booked' ? (
                          <div className="text-xs text-amber-500 p-2 sm:p-3 border rounded-md bg-muted/30">
                            {appointmentStatus === 'completed' && "Tamamlanmış randevuların saati değiştirilemez."}
                            {appointmentStatus === 'cancelled' && "İptal edilmiş randevuların saati değiştirilemez."}
                            {appointmentStatus === 'no-show' && "Gelmemiş randevuların saati değiştirilemez."}
                          </div>
                        ) : isTimeSelectionEnabled ? (
                          availableTimes.length > 0 ? (
                            <TimeSlotGrid
                              date={selectedDate}
                              availableTimes={availableTimes}
                              selectedTime={field.value}
                              onTimeSelect={field.onChange}
                              holidayDates={holidayDates}
                              barberWorkingHours={barberWorkingHoursList}
                              existingAppointmentTimes={existingAppointmentTimes}
                            />
                          ) : (
                            <div className="text-xs text-muted-foreground p-2 sm:p-3 border rounded-md bg-muted/30">
                              Müsait saat bulunamadı
                            </div>
                          )
                        ) : (
                          <div className="text-xs text-muted-foreground p-2 sm:p-3 border rounded-md bg-muted/30">
                            Önce tarih ve berber seçmelisiniz
                          </div>
                        )}
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Müşteri Bilgileri */}
          <div className="bg-muted/40 p-3 sm:p-4 rounded-lg border">
            <h3 className="text-sm font-medium mb-2 sm:mb-3">Müşteri Bilgileri</h3>
            <Tabs value={customerMode} onValueChange={(value) => setCustomerMode(value as "new" | "existing")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-3 sm:mb-4">
                <TabsTrigger value="new" className="text-xs sm:text-sm py-1.5 sm:py-2">Yeni Müşteri</TabsTrigger>
                <TabsTrigger value="existing" className="text-xs sm:text-sm py-1.5 sm:py-2">Mevcut Müşteri</TabsTrigger>
              </TabsList>

              <TabsContent value="new" className="space-y-3">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="fullname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Müşteri Adı</FormLabel>
                        <FormControl>
                          <Input placeholder="Müşteri Adı" {...field} className="h-9 text-sm w-full" />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phonenumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Telefon</FormLabel>
                        <FormControl>
                          <Input placeholder="Telefon" {...field} className="h-9 text-sm w-full" />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <div className="col-span-1 sm:col-span-2">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs">E-posta (İsteğe bağlı)</FormLabel>
                          <FormControl>
                            <Input placeholder="E-posta" {...field} className="h-9 text-sm w-full" />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="existing">
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-1/2 h-3.5 w-3.5 sm:h-4 sm:w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Müşteri ara (isim veya telefon)"
                      className="pl-8 sm:pl-10 h-9 text-sm w-full"
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value)
                        // searchCustomers is now called via the debounced effect
                      }}
                    />
                  </div>

                  <div className="max-h-[25vh] sm:max-h-[30vh] overflow-y-auto border rounded-md scrollbar-hide">
                    {isSearching ? (
                      <div className="text-center py-2 sm:py-3 text-xs sm:text-sm">Aranıyor...</div>
                    ) : searchResults.length > 0 ? (
                      <div className="divide-y">
                        {searchResults.map((customer) => (
                          <div
                            key={customer.id}
                            className="p-2 cursor-pointer hover:bg-muted transition-colors"
                            onClick={() => handleCustomerSelect(customer)}
                          >
                            <div className="font-medium text-sm">{customer.name} {customer.surname}</div>
                            <div className="text-xs text-muted-foreground">{customer.phone}</div>
                            {customer.email && (
                              <div className="text-xs text-muted-foreground">{customer.email}</div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : debouncedSearchQuery.length > 1 ? (
                      <div className="text-center py-2 sm:py-3 text-xs sm:text-sm">Müşteri bulunamadı</div>
                    ) : (
                      <div className="text-center py-2 sm:py-3 text-xs sm:text-sm">Müşteri aramak için en az 2 karakter girin</div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Notlar */}
          <div className="bg-muted/40 p-3 sm:p-4 rounded-lg border">
            <h3 className="text-sm font-medium mb-2 sm:mb-3">Notlar</h3>
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="Randevu ile ilgili notlar..."
                      className="resize-none min-h-[60px] sm:min-h-[80px] md:min-h-[120px] text-sm w-full"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-between sm:justify-end gap-2 sm:gap-4 mt-3 sm:mt-4 pt-3 border-t">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="flex-1 sm:flex-initial h-9 sm:h-10 text-xs sm:text-sm"
            onClick={() => {
              if (onCancel) {
                onCancel();
              } else {
                router.back();
              }
            }}
          >
            İptal
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            size="sm"
            className="flex-1 sm:flex-initial h-9 sm:h-10 text-xs sm:text-sm"
          >
            {isLoading ? "Kaydediliyor..." : appointmentId ? "Güncelle" : "Oluştur"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
