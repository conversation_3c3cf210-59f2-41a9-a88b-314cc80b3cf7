"use client"

import { useState, useEffect } from "react"
import { Plus, GripVertical } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import { Button } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { services } from "@/lib/db"
import type { Service as ServiceType } from "@/lib/db/types"
import { useUser } from "@/contexts/UserContext"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "Hizmet adı en az 2 karakter olmalıdır.",
  }),
  description: z.string().optional(),
  duration: z.coerce.number().min(5, {
    message: "Süre en az 5 dakika olmalıdır.",
  }),
  price: z.coerce.number().min(0, {
    message: "Fiyat 0 veya daha yüksek olmalıdır.",
  }),
})

// We're using the Service type from lib/db/types.ts

// Sortable Service Card Component
function SortableServiceCard({
  service,
  onEdit,
  onDelete
}: {
  service: ServiceType;
  onEdit: (service: ServiceType) => void;
  onDelete: (id: string) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: service.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <Card ref={setNodeRef} style={style} className={isDragging ? "shadow-lg" : ""}>
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        <div className="flex items-center space-x-2 flex-1">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
          >
            <GripVertical className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-lg">{service.name}</CardTitle>
            <CardDescription>
              <div className="flex justify-between items-center">
                <span>{service.duration} dakika</span>
                <span className="font-medium">{service.price} ₺</span>
              </div>
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          {service.description || "Açıklama bulunmuyor."}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(service)}
        >
          Düzenle
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={() => onDelete(service.id)}
        >
          Sil
        </Button>
      </CardFooter>
    </Card>
  )
}

export default function ServicesPage() {
  const [servicesList, setServicesList] = useState<ServiceType[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  // Artık salonId UserContext'ten geliyor
  const [editingService, setEditingService] = useState<ServiceType | null>(null)

  // UserContext'ten salon bilgilerini al
  const { salonId, salonLoading } = useUser()

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Load services
  useEffect(() => {
    async function loadServicesData() {
      if (!salonId || salonLoading) return

      setLoading(true)
      try {
        // Load services for this salon
        await loadServices(salonId)
      } catch (error) {
        console.error("Error:", error)
        toast.error("Bir hata oluştu.")
        setLoading(false)
      }
    }

    loadServicesData()
  }, [salonId, salonLoading])

  // Load services
  async function loadServices(salonId: string) {
    try {
      const data = await services.getServices(salonId)
      setServicesList(data)
    } catch (error) {
      console.error("Error loading services:", error)
      toast.error("Hizmetler yüklenirken bir hata oluştu.")
    } finally {
      setLoading(false)
    }
  }

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      duration: 30,
      price: 0,
    },
  })

  // Set form values when editing
  useEffect(() => {
    if (editingService) {
      form.reset({
        name: editingService.name,
        description: editingService.description || "",
        duration: editingService.duration,
        price: editingService.price || 0,
      })
      setShowForm(true)
    }
  }, [editingService, form])

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!salonId) {
      toast.error("Salon bilgisi bulunamadı.")
      return
    }

    try {
      if (editingService) {
        // Update existing service
        await services.updateService({
          id: editingService.id,
          name: values.name,
          description: values.description || null,
          duration: values.duration,
          price: values.price,
        })

        toast.success("Hizmet başarıyla güncellendi!")
      } else {
        // Create new service
        await services.createService({
          salon_id: salonId,
          name: values.name,
          description: values.description || null,
          duration: values.duration,
          price: values.price,
        })

        toast.success("Hizmet başarıyla eklendi!")
      }

      // Reload services
      await loadServices(salonId)

      // Reset form and state
      form.reset()
      setShowForm(false)
      setEditingService(null)
    } catch (error) {
      console.error("Error saving service:", error)
      toast.error("Hizmet kaydedilirken bir hata oluştu.")
    }
  }

  // Handle service deletion
  async function handleDelete(id: string) {
    if (!confirm("Bu hizmeti silmek istediğinizden emin misiniz?")) {
      return
    }

    try {
      await services.deleteService(id)
      toast.success("Hizmet başarıyla silindi!")

      // Reload services
      if (salonId) {
        await loadServices(salonId)
      }
    } catch (error) {
      console.error("Error deleting service:", error)
      toast.error("Hizmet silinirken bir hata oluştu.")
    }
  }

  // Handle edit button click
  function handleEdit(service: ServiceType) {
    setEditingService(service)
  }

  // Handle cancel button click
  function handleCancel() {
    setShowForm(false)
    setEditingService(null)
    form.reset()
  }

  // Handle drag end
  async function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (!over || active.id === over.id) {
      return
    }

    const oldIndex = servicesList.findIndex(service => service.id === active.id)
    const newIndex = servicesList.findIndex(service => service.id === over.id)

    if (oldIndex === -1 || newIndex === -1) {
      return
    }

    // Update local state immediately for better UX
    const newServicesList = arrayMove(servicesList, oldIndex, newIndex)
    setServicesList(newServicesList)

    try {
      // Prepare the new order data
      const serviceOrders = newServicesList.map((service, index) => ({
        id: service.id,
        sort_order: index + 1
      }))

      // Update the database
      if (salonId) {
        await services.updateServicesOrder(salonId, serviceOrders)
        toast.success("Hizmet sıralaması güncellendi!")
      }
    } catch (error) {
      console.error("Error updating service order:", error)
      toast.error("Sıralama güncellenirken bir hata oluştu.")
      // Revert the local state on error
      if (salonId) {
        await loadServices(salonId)
      }
    }
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Hizmetler</h1>
        </div>
        <Button
          onClick={() => {
            setEditingService(null)
            form.reset()
            setShowForm(!showForm)
          }}
          disabled={loading}
        >
          <Plus className="mr-2 h-4 w-4" />
          Hizmet Ekle
        </Button>
      </header>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingService ? "Hizmeti Düzenle" : "Yeni Hizmet Ekle"}</CardTitle>
            <CardDescription>
              {editingService ? "Mevcut bir hizmeti düzenleyin." : "Salonunuzun sunduğu yeni bir hizmet ekleyin."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hizmet Adı</FormLabel>
                      <FormControl>
                        <Input placeholder="Saç Kesimi" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Açıklama (İsteğe bağlı)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Hizmetin kısa bir açıklaması"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="duration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Süre (dakika)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={5}
                            step={5}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fiyat (₺)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            step={1}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                  >
                    İptal
                  </Button>
                  <Button type="submit">
                    {editingService ? "Hizmeti Güncelle" : "Hizmeti Kaydet"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p>Yükleniyor...</p>
        </div>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <div className="space-y-4">
            {servicesList.length > 0 ? (
              <div className="mb-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Hizmetleri sürükleyerek sıralayabilirsiniz. Bu sıralama müşteri randevu alma ekranında da geçerli olacaktır.
                </p>
                <SortableContext
                  items={servicesList.map(service => service.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {servicesList.map((service) => (
                      <SortableServiceCard
                        key={service.id}
                        service={service}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                      />
                    ))}
                  </div>
                </SortableContext>
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  Henüz hizmet eklenmemiş. İlk hizmetinizi oluşturmak için "Hizmet Ekle" düğmesine tıklayın.
                </p>
              </div>
            )}
          </div>
        </DndContext>
      )}
    </div>
  )
}
