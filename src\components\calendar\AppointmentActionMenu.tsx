"use client"

import { useState } from "react"
import { Edit, Trash2, Eye, MoreHorizontal, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { isAfter, parseISO, startOfDay } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { Appointment } from "@/lib/db/types"
import { appointments as appointmentsApi } from "@/lib/db"
import { useIsMobile } from "@/hooks/use-mobile"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface AppointmentActionMenuProps {
  appointment: Appointment & {
    customers?: {
      name: string;
      surname: string;
      phone: string;
    };
    barbers?: {
      name: string;
    };
    services?: {
      name: string;
      duration: number;
    };
  }
  onAppointmentUpdated?: () => void
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
}

export function AppointmentActionMenu({
  appointment,
  onAppointmentUpdated,
  isOpen: controlledIsOpen,
  onOpenChange: controlledOnOpenChange
}: AppointmentActionMenuProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [internalIsOpen, setInternalIsOpen] = useState(false)
  const router = useRouter()
  const isMobile = useIsMobile()

  // Use controlled or uncontrolled state
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen
  const setIsOpen = controlledOnOpenChange || setInternalIsOpen

  // Randevunun geçmiş olup olmadığını kontrol et
  const isAppointmentInPast = () => {
    const today = startOfDay(new Date())
    const appointmentDate = parseISO(appointment.date)
    return !isAfter(appointmentDate, today) // Bugün veya daha önceki bir tarih ise geçmiş randevudur
  }

  // Randevunun aktif olup olmadığını kontrol et
  const isActive = appointment.status === 'booked'

  // Randevunun gelecekte olup olmadığını kontrol et
  const isFuture = !isAppointmentInPast()

  // Handle view appointment
  const handleViewAppointment = () => {
    setIsOpen(false)
    router.push(`/dashboard/appointments/${appointment.id}`)
  }

  // Handle edit appointment
  const handleEditAppointment = () => {
    setIsOpen(false)
    router.push(`/dashboard/appointments/${appointment.id}/edit`)
  }

  // Handle delete appointment
  const handleDeleteAppointment = async () => {
    try {
      await appointmentsApi.deleteAppointment(appointment.id)
      toast.success("Randevu başarıyla silindi")
      if (onAppointmentUpdated) {
        onAppointmentUpdated()
      }
    } catch (error) {
      console.error("Error deleting appointment:", error)
      toast.error("Randevu silinirken bir hata oluştu")
    } finally {
      setIsDeleteDialogOpen(false)
      setIsOpen(false)
    }
  }

  // Handle appointment status changes
  const handleStatusChange = async (status: 'completed' | 'cancelled' | 'no-show') => {
    try {
      let message = ""

      switch (status) {
        case 'completed':
          await appointmentsApi.completeAppointment(appointment.id)
          message = "Randevu tamamlandı olarak işaretlendi"
          break
        case 'cancelled':
          await appointmentsApi.cancelAppointment(appointment.id)
          message = "Randevu iptal edildi"
          break
        case 'no-show':
          await appointmentsApi.markAppointmentAsNoShow(appointment.id)
          message = "Randevu gelmedi olarak işaretlendi"
          break
      }

      toast.success(message)

      // Güncel veriyi almak için callback'i çağır
      if (onAppointmentUpdated) {
        onAppointmentUpdated()
      }
    } catch (error) {
      console.error("Error updating appointment status:", error)
      toast.error("Randevu durumu güncellenirken bir hata oluştu")
    } finally {
      setIsOpen(false)
    }
  }

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 absolute top-1 right-1 opacity-70 hover:opacity-100 bg-background/80 hover:bg-background z-50 menu-button"
            onClick={(e) => {
              e.stopPropagation()
              e.preventDefault()
              setIsOpen(!isOpen) // Toggle menu state
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              e.preventDefault()
            }}
            onTouchStart={(e) => {
              e.stopPropagation()
            }}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-52 p-2 menu-content"
          align={isMobile ? "center" : "end"}
          side={isMobile ? "bottom" : "right"}
          sideOffset={5}
        >
          <div className="flex flex-col space-y-1">
            <Button
              variant="ghost"
              size="sm"
              className="justify-start"
              onClick={handleViewAppointment}
            >
              <Eye className="mr-2 h-4 w-4" />
              Görüntüle
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="justify-start"
              onClick={handleEditAppointment}
            >
              <Edit className="mr-2 h-4 w-4" />
              Düzenle
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="justify-start text-destructive hover:text-destructive"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Sil
            </Button>

            {/* Sadece aktif randevular için durum değiştirme seçeneklerini göster */}
            {isActive && (
              <>
                <Separator className="my-1" />
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-950/50"
                  onClick={() => handleStatusChange('completed')}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Tamamlandı
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start text-amber-600 hover:text-amber-700 hover:bg-amber-50 dark:hover:bg-amber-950/50"
                  onClick={() => handleStatusChange('no-show')}
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Gelmedi
                </Button>
                {/* İptal Et butonu sadece gelecekteki randevular için gösterilir */}
                {isFuture && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/50"
                    onClick={() => handleStatusChange('cancelled')}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    İptal Et
                  </Button>
                )}
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Randevuyu silmek istediğinize emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu randevu kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAppointment}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
