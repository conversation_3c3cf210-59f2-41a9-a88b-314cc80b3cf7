"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
  useMemo,
} from "react";
import { User } from "@supabase/supabase-js";
import { barbers, salons } from "@/lib/db";
import { toast } from "sonner";
import { Salon } from "@/lib/db/types";
import { supabaseClient } from '@/lib/supabase-singleton';


const supabase = supabaseClient;


// Kullanıcı rolü tipi
type UserRole = "owner" | "staff" | "new_user" | "admin" | "unknown";

// Context'in tutacağı değerler
type UserContextType = {
  user: User | null;
  userRole: UserRole;
  isLoading: boolean;
  error: string | null;
  salon: Salon | null;
  salonId: string | null;
  salonLoading: boolean;
  salonError: string | null;
  isAdminUser: boolean;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
};

// Default de<PERSON><PERSON><PERSON>
const defaultContext: UserContextType = {
  user: null,
  userRole: "unknown",
  isLoading: true,
  error: null,
  salon: null,
  salonId: null,
  salonLoading: true,
  salonError: null,
  isAdminUser: false,
  signOut: async () => {},
  refreshUser: async () => {},
};

// Context'i oluştur
const UserContext = createContext<UserContextType>(defaultContext);

// Provider bileşeni
export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<UserRole>("unknown");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [salon, setSalon] = useState<Salon | null>(null);
  const [salonId, setSalonId] = useState<string | null>(null);
  const [salonLoading, setSalonLoading] = useState(true);
  const [salonError, setSalonError] = useState<string | null>(null);

  // Admin kullanıcı kontrolü - userRole'e göre hesapla
  const isAdminUser = useMemo(() => userRole === "admin", [userRole]);

  // Kullanıcı bilgilerini yükleme durumunu takip et
  const [userDataFetched, setUserDataFetched] = useState(false);

  // Salon bilgilerini al - useCallback ile memoize et
  const fetchSalon = useCallback(async (userId: string) => {
    if (!userId) return;

    try {
      setSalonLoading(true);
      setSalonError(null);

      const { data, error } = await salons.getSalonByOwnerId(userId);

      if (error) {
        console.error("Salon bilgisi alınamadı:", error);
        setSalonError("Salon bilgisi alınamadı");
        setSalon(null);
        setSalonId(null);
        return;
      }

      if (!data) {
        setSalon(null);
        setSalonId(null);
        return;
      }

      setSalon(data);
      setSalonId(data.id);
    } catch (err) {
      console.error("Salon bilgisi alınırken hata:", err);
      setSalonError("Beklenmeyen bir hata oluştu");
    } finally {
      setSalonLoading(false);
    }
  }, []);

  // Kullanıcı rolünü belirle - useCallback ile memoize et
  const determineUserRole = useCallback(async (user: User) => {
    if (!user) return;

    try {
      // Admin kontrolü
      const { data: isAdmin, error: adminError } = await supabase.rpc(
        "is_admin"
      );

      if (adminError) {
        console.error("Admin kontrolü hatası:", adminError);
      } else if (isAdmin) {
        setUserRole("admin");
        return;
      }

      // Staff kontrolü
      const staffMember = await barbers.getBarberByUserId(user.id);
      if (staffMember) {
        setUserRole("staff");
        return;
      }

      // Salon sahibi kontrolü
      const { data: salon } = await supabase
        .from("salons")
        .select("id")
        .eq("owner_id", user.id)
        .maybeSingle();

      if (salon) {
        setUserRole("owner");
        return;
      }

      // Kullanıcı ne salon sahibi ne de personel ise, yeni kullanıcı olarak işaretle
      setUserRole("new_user");
    } catch (err) {
      console.error("Kullanıcı rolü belirlenirken hata:", err);
      setUserRole("new_user");
    }
  }, []);

  // Kullanıcı bilgilerini al - useCallback ile memoize et
  const fetchUser = useCallback(async () => {
    // Eğer kullanıcı bilgileri zaten yüklendiyse ve kullanıcı varsa, tekrar yükleme
    if (userDataFetched && user) {
      setIsLoading(false); // Yükleme durumunu kapat
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.getUser();

      if (error) {
        console.error("Kullanıcı bilgisi alınamadı:", error);
        setError("Kullanıcı bilgisi alınamadı");
        setUser(null);
        setUserRole("new_user");
        setSalon(null);
        setSalonId(null);
        setUserDataFetched(true); // Hata durumunda da veri çekme işleminin tamamlandığını işaretle
        return;
      }

      if (!data.user) {
        setUser(null);
        setUserRole("new_user");
        setSalon(null);
        setSalonId(null);
        setUserDataFetched(true); // Kullanıcı yoksa da veri çekme işleminin tamamlandığını işaretle
        return;
      }

      setUser(data.user);
      await determineUserRole(data.user);

      // Kullanıcı bilgisi alındıktan sonra, eğer admin değilse salon bilgisini al
      if (data.user && !isAdminUser) {
        await fetchSalon(data.user.id);
      }

      // Kullanıcı bilgilerinin yüklendiğini işaretle
      setUserDataFetched(true);
    } catch (err) {
      console.error("Kullanıcı bilgisi alınırken hata:", err);
      setError("Beklenmeyen bir hata oluştu");
      setUserDataFetched(true); // Hata durumunda da veri çekme işleminin tamamlandığını işaretle
    } finally {
      setIsLoading(false);
    }
  }, [determineUserRole, fetchSalon, user, userDataFetched, isAdminUser]);

  // Çıkış yap - useCallback ile memoize et
  const signOut = useCallback(async () => {
    try {
      // Önce state'i temizle
      setUser(null);
      setUserRole("new_user");
      setSalon(null);
      setSalonId(null);
      setUserDataFetched(false);

      // Sonra Supabase'den çıkış yap
      await supabase.auth.signOut();

      toast.success("Başarıyla çıkış yapıldı");
      window.location.href = "/auth/login";
    } catch (err) {
      console.error("Çıkış yapılırken hata:", err);
      toast.error("Çıkış yapılırken bir hata oluştu");
    }
  }, []);

  // Kullanıcı bilgilerini yenile - useCallback ile memoize et
  const refreshUser = useCallback(async () => {
    setUserDataFetched(false);
    await fetchUser();
  }, [fetchUser]);

  // Sayfa yüklendiğinde kullanıcı bilgilerini al - sadece bir kez
  useEffect(() => {
    // Sadece ilk yüklemede veri çek
    if (!userDataFetched) {
      fetchUser();
    }
  }, [fetchUser, userDataFetched]);

  // Context değerlerini memoize et
  const contextValue = useMemo(
    () => ({
      user,
      userRole,
      isLoading,
      error,
      salon,
      salonId,
      salonLoading,
      salonError,
      isAdminUser,
      signOut,
      refreshUser,
    }),
    [
      user,
      userRole,
      isLoading,
      error,
      salon,
      salonId,
      salonLoading,
      salonError,
      isAdminUser,
      signOut,
      refreshUser,
    ]
  );

  return (
    <UserContext.Provider value={contextValue}>{children}</UserContext.Provider>
  );
}

// Custom hook
export function useUser() {
  return useContext(UserContext);
}
