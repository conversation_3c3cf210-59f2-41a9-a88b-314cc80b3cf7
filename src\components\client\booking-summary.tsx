"use client"

import { motion } from "framer-motion"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { Scissors, User, Calendar, Clock, UserCircle, Phone, Mail, ArrowLeft, CheckCircle } from "lucide-react"
import { Service, Barber } from "@/lib/db/types"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface BookingSummaryProps {
  service: Service | null
  barber: Barber | undefined
  date: Date | null
  time: string
  customerInfo: {
    name: string
    surname: string
    phone: string
    email: string | undefined
  }
  onSubmit: () => void
  onBack: () => void
  isLoading: boolean
  barberWorkingHours?: any[] // Add working hours to determine midnight crossing
}

export function BookingSummary({
  service,
  barber,
  date,
  time,
  customerInfo,
  onSubmit,
  onBack,
  isLoading,
  barberWorkingHours = []
}: BookingSummaryProps) {

  // Calculate the actual appointment date (considering midnight crossing)
  const getActualAppointmentDate = () => {
    if (!date || !barber || !barberWorkingHours.length) return date

    const dayOfWeek = date.getDay()
    const workingHoursForDay = barberWorkingHours.find((wh: any) =>
      wh.barber_id === barber.id && wh.day_of_week === dayOfWeek
    )

    if (!workingHoursForDay) return date

    const openTime = workingHoursForDay.open_time.substring(0, 5)
    const closeTime = workingHoursForDay.close_time.substring(0, 5)
    const crossesMidnight = closeTime < openTime

    if (crossesMidnight) {
      const timeSlotHour = parseInt(time.split(':')[0])
      const closeHour = parseInt(closeTime.split(':')[0])
      const isNextDaySlot = timeSlotHour <= closeHour

      if (isNextDaySlot) {
        return new Date(date.getTime() + 24 * 60 * 60 * 1000)
      }
    }

    return date
  }

  const actualDate = getActualAppointmentDate()


  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Randevu Özeti</h2>
        <p className="text-muted-foreground">
          Randevu bilgilerinizi kontrol edin ve onaylayın
        </p>
      </div>

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="bg-primary/10 p-4 border-b">
            <h3 className="font-semibold text-lg">Randevu Detayları</h3>
          </div>

          <div className="p-4 space-y-4">
            {/* Service */}
            <div className="flex items-start space-x-3">
              <div className="bg-muted p-2 rounded-md">
                <Scissors className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Hizmet</div>
                <div className="font-medium">{service?.name}</div>
                <div className="flex items-center mt-1">
                  <div className="text-sm text-muted-foreground">
                    {service?.duration} dakika
                  </div>
                </div>
              </div>
            </div>

            {/* Barber */}
            <div className="flex items-start space-x-3">
              <div className="bg-muted p-2 rounded-md">
                <User className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Berber</div>
                <div className="font-medium">{barber?.name}</div>
              </div>
            </div>

            {/* Date and Time */}
            <div className="flex items-start space-x-3">
              <div className="bg-muted p-2 rounded-md">
                <Calendar className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Tarih ve Saat</div>
                <div className="font-medium">
                  {actualDate ? `${format(actualDate, "d MMMM yyyy", { locale: tr })}, ${time}` : time}
                </div>
                <div className="text-sm text-muted-foreground">
                  {actualDate && format(actualDate, "EEEE", { locale: tr })}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="bg-primary/10 p-4 border-b">
            <h3 className="font-semibold text-lg">Kişisel Bilgiler</h3>
          </div>

          <div className="p-4 space-y-4">
            {/* Name */}
            <div className="flex items-start space-x-3">
              <div className="bg-muted p-2 rounded-md">
                <UserCircle className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">İsim Soyisim</div>
                <div className="font-medium">
                  {customerInfo.name} {customerInfo.surname}
                </div>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-start space-x-3">
              <div className="bg-muted p-2 rounded-md">
                <Phone className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Telefon</div>
                <div className="font-medium">{customerInfo.phone}</div>
              </div>
            </div>

            {/* Email */}
            {customerInfo.email && (
              <div className="flex items-start space-x-3">
                <div className="bg-muted p-2 rounded-md">
                  <Mail className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">E-posta</div>
                  <div className="font-medium">{customerInfo.email}</div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="pt-4 flex flex-col sm:flex-row gap-3">
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-1/2"
          size="lg"
          disabled={isLoading}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Geri
        </Button>

        <Button
          onClick={onSubmit}
          className="w-full sm:w-1/2"
          size="lg"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="h-5 w-5 border-2 border-current border-t-transparent animate-spin rounded-full mr-2"></div>
              Randevu Oluşturuluyor...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-5 w-5" />
              Randevuyu Onayla
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
