"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { format, addMinutes, parse, isAfter, isBefore } from "date-fns"
import * as z from "zod"
import { toast } from "sonner"
import { motion } from "framer-motion"
import { notifyNewAppointment, formatAppointmentForNotification } from "@/lib/utils/telegram-notifications"

import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ServiceSelector } from "@/components/client/service-selector"
import { BarberSelector } from "@/components/client/barber-selector"
import { DateSelector } from "@/components/client/date-selector"
import { TimeSelector } from "@/components/client/time-selector"
import { CustomerInfoForm } from "@/components/client/customer-info-form"
import { BookingSummary } from "@/components/client/booking-summary"
import { StepIndicator } from "@/components/client/step-indicator"
import {
  appointments,
  publicAccess,
  Barber,
  Service,
  BarberWorkingHours
} from "@/lib/db"
import { supabaseClient } from "@/lib/supabase-singleton"

// Form schema
const formSchema = z.object({
  date: z.date({
    required_error: "Tarih seçmelisiniz.",
  }).optional(),
  start_time: z.string({
    required_error: "Saat seçmelisiniz.",
  }).optional().or(z.literal("")),
  barber_id: z.string({
    required_error: "Berber seçmelisiniz.",
  }).optional().or(z.literal("")),
  service_id: z.string({
    required_error: "Hizmet seçmelisiniz.",
  }).optional().or(z.literal("")),
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  surname: z.string().min(2, {
    message: "Soyisim en az 2 karakter olmalıdır.",
  }),
  phone: z.string().min(10, {
    message: "Geçerli bir telefon numarası giriniz.",
  }),
  email: z.string().email({
    message: "Geçerli bir e-posta adresi giriniz.",
  }).optional().or(z.literal("")),
})

interface ClientBookingFormProps {
  salonId: string
  onSuccess?: () => void
}

type BookingStep = "service" | "barber" | "date" | "time" | "info" | "summary"

export function ClientBookingForm({ salonId, onSuccess }: ClientBookingFormProps) {
  const [currentStep, setCurrentStep] = useState<BookingStep>("service")
  const [isLoading, setIsLoading] = useState(false)
  const [barbersList, setBarbersList] = useState<Barber[]>([])
  const [servicesList, setServicesList] = useState<Service[]>([])
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedBarber, setSelectedBarber] = useState<string | null>(null)
  const [disabledDates, setDisabledDates] = useState<Date[]>([])
  const [holidayDates, setHolidayDates] = useState<{ date: Date; description: string }[]>([])
  const [barberWorkingHoursList, setBarberWorkingHoursList] = useState<BarberWorkingHours[]>([])
  const [existingAppointmentTimes, setExistingAppointmentTimes] = useState<string[]>([])

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      surname: "",
      phone: "",
      email: "",
      start_time: "",
      service_id: "",
      barber_id: ""
    },
  })

  // Load services and barbers
  useEffect(() => {
    async function loadData() {
      try {
        // Load services
        const servicesData = await publicAccess.getPublicServicesBySalonId(salonId)
        setServicesList(servicesData)

        // Load barbers
        const barbersData = await publicAccess.getPublicBarbersBySalonId(salonId)
        setBarbersList(barbersData)
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Veriler yüklenirken bir hata oluştu.")
      }
    }

    loadData()
  }, [salonId])

  // Handle service selection
  const handleServiceChange = (serviceId: string) => {
    const service = servicesList.find(s => s.id === serviceId)
    setSelectedService(service || null)
    form.setValue("service_id", serviceId)

    // Reset barber selection if service changes
    if (form.getValues("barber_id") && currentStep === "service") {
      form.setValue("barber_id", "")
      setSelectedBarber(null)
    }

    // Move to next step
    if (currentStep === "service") {
      setCurrentStep("barber")
    }
  }

  // Handle barber selection
  const handleBarberChange = async (barberId: string) => {
    setSelectedBarber(barberId)
    form.setValue("barber_id", barberId)
    form.setValue("start_time", "")

    // Load barber working hours
    await loadBarberWorkingHours(barberId)

    // Load available times when both date and barber are selected
    if (selectedDate && barberId) {
      await loadAvailableTimes(selectedDate, barberId)
    }

    // Move to next step
    if (currentStep === "barber") {
      setCurrentStep("date")
    }
  }

  // Handle date selection
  const handleDateChange = async (date: Date) => {
    setSelectedDate(date)
    form.setValue("date", date)
    form.setValue("start_time", "")

    // Load available times when both date and barber are selected
    if (date && selectedBarber) {
      await loadAvailableTimes(date, selectedBarber)
    }

    // Move to next step only if we have a barber selected
    if (currentStep === "date" && selectedBarber) {
      setCurrentStep("time")
    }
  }

  // Handle time selection
  const handleTimeChange = (time: string) => {
    form.setValue("start_time", time)

    // Move to next step
    if (currentStep === "time") {
      setCurrentStep("info")
    }
  }

  // Handle customer info submission
  const handleCustomerInfoSubmit = (data: { name: string, surname: string, phone: string, email: string }) => {
    form.setValue("name", data.name)
    form.setValue("surname", data.surname)
    form.setValue("phone", data.phone)
    form.setValue("email", data.email || "")

    // Move to next step
    setCurrentStep("summary")
  }

  // Load barber working hours and set disabled dates
  const loadBarberWorkingHours = async (barberId: string) => {
    try {
      // Berber çalışma saatlerini getir
      // Not: Bu fonksiyon SQL fonksiyonları Supabase'de çalıştırıldıktan sonra çalışacaktır
      let barberWorkingHours = []
      try {
        const { data, error } = await supabaseClient
          .from('barber_working_hours')
          .select('*')
          .eq('barber_id', barberId)
          .order('day_of_week')

        if (!error) {
          barberWorkingHours = data || []
        }
      } catch (err) {
        console.error("Error fetching barber working hours:", err)
      }

      // Salon çalışma saatlerini getir
      const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)

      // Berber çalışma saatleri varsa onları, yoksa salon çalışma saatlerini kullan
      const hoursToUse = barberWorkingHours.length > 0
        ? barberWorkingHours.map(bwh => ({
            id: bwh.id,
            barber_id: bwh.barber_id,
            salon_id: salonId,
            day_of_week: bwh.day_of_week,
            open_time: bwh.open_time,
            close_time: bwh.close_time,
            is_closed: bwh.is_closed,
            lunch_start_time: bwh.lunch_start_time,
            lunch_end_time: bwh.lunch_end_time,
            has_lunch_break: bwh.has_lunch_break
          }))
        : salonWorkingHours.map(wh => ({
            id: wh.id,
            barber_id: barberId,
            salon_id: wh.salon_id,
            day_of_week: wh.day_of_week,
            open_time: wh.open_time,
            close_time: wh.close_time,
            is_closed: wh.is_closed,
            lunch_start_time: null,
            lunch_end_time: null,
            has_lunch_break: false
          }))

      // Set barber working hours list for TimeSlotGrid
      setBarberWorkingHoursList(hoursToUse || [])

      // All days of the week (0-6)
      const allDaysOfWeek = [0, 1, 2, 3, 4, 5, 6]

      // Days that have records in the database
      const daysWithRecords = hoursToUse.map(day => day.day_of_week)

      // Days that don't have records in the database (considered closed)
      const daysWithoutRecords = allDaysOfWeek.filter(day => !daysWithRecords.includes(day))

      // Days that are explicitly marked as closed
      const explicitlyClosedDays = hoursToUse
        .filter(day => day.is_closed)
        .map(day => day.day_of_week)

      // All closed days (either no record or explicitly marked as closed)
      const closedDays = [...new Set([...daysWithoutRecords, ...explicitlyClosedDays])]

      // Generate disabled dates for the next 3 months
      const disabledDatesArray: Date[] = []
      const today = new Date()
      const threeMonthsLater = new Date()
      threeMonthsLater.setMonth(today.getMonth() + 3)

      // Get salon holidays
      const holidaysData = await publicAccess.getPublicHolidaysBySalonId(salonId)

      // Format holiday dates
      const holidayDateObjects = (holidaysData || []).map(holiday => new Date(holiday.date))
      const holidayDateFormatted = (holidaysData || []).map(holiday => ({
        date: new Date(holiday.date),
        description: holiday.description || 'Tatil'
      }))

      setHolidayDates(holidayDateFormatted)

      // Loop through each day in the next 3 months
      const currentDate = new Date(today)
      while (currentDate <= threeMonthsLater) {
        const dayOfWeek = currentDate.getDay()
        const currentDateStr = format(currentDate, "yyyy-MM-dd")

        // If the barber doesn't work on this day of the week, add it to disabled dates
        if (closedDays.includes(dayOfWeek)) {
          // Create a new Date object to avoid reference issues
          disabledDatesArray.push(new Date(currentDate.getTime()))
        }
        // Check if the date is a holiday
        else if (holidayDateObjects.some(date => format(date, "yyyy-MM-dd") === currentDateStr)) {
          disabledDatesArray.push(new Date(currentDate.getTime()))
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1)
      }

      setDisabledDates(disabledDatesArray)
    } catch (error) {
      console.error("Error loading barber working hours:", error)
      toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu.")
    }
  }

  // Load available times for a specific date and barber
  const loadAvailableTimes = async (date: Date, barberId: string) => {
    try {
      const formattedDate = format(date, "yyyy-MM-dd")
      const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday

      // Check if the selected date is a holiday
      const holidaysData = await publicAccess.getPublicHolidaysBySalonId(salonId)
      const holidayData = holidaysData?.find(h => h.date === formattedDate)

      // If the selected date is a holiday, show a message and return
      if (holidayData) {
        setAvailableTimes([])
        const holidayDescription = holidayData.description ? ` (${holidayData.description})` : ''
        toast.error(`Seçilen tarih tatil günüdür${holidayDescription}. Lütfen başka bir tarih seçin.`)
        return
      }

      // Get barber working hours for the selected day from the already loaded data
      const workingHoursData = barberWorkingHoursList.find(wh =>
        wh.barber_id === barberId && wh.day_of_week === dayOfWeek
      )

      // Check if barber is working on the selected day
      if (!workingHoursData || workingHoursData.is_closed) {
        setAvailableTimes([])
        toast.error("Seçilen berber bu gün çalışmıyor. Lütfen başka bir gün veya berber seçin.")
        return
      }

      // Parse working hours first
      const openTime = workingHoursData.open_time.substring(0, 5)
      const closeTime = workingHoursData.close_time.substring(0, 5)

      // Check if working hours cross midnight (close time < open time)
      const crossesMidnight = closeTime < openTime

      // Get existing appointments for the selected date and potentially next day
      let existingAppointments = []
      if (crossesMidnight) {
        // If working hours cross midnight, get appointments for both current and next day
        const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000)
        const nextFormattedDate = format(nextDate, "yyyy-MM-dd")

        const currentDayAppointments = await publicAccess.getPublicAppointmentsBySalonId(
          salonId,
          formattedDate,
          formattedDate
        )

        const nextDayAppointments = await publicAccess.getPublicAppointmentsBySalonId(
          salonId,
          nextFormattedDate,
          nextFormattedDate
        )

        existingAppointments = [...(currentDayAppointments || []), ...(nextDayAppointments || [])]
      } else {
        // Normal working hours within same day
        existingAppointments = await publicAccess.getPublicAppointmentsBySalonId(
          salonId,
          formattedDate,
          formattedDate
        )
      }

      // Set existing appointment times for the TimeSlotGrid component
      const existingTimes = (existingAppointments || []).map(apt => apt.start_time.substring(0, 5))
      setExistingAppointmentTimes(existingTimes)
      console.log("loadAvailableTimes - existingTimes:", existingTimes)

      // If we don't have a selected service, we can't calculate available times
      if (!selectedService) {
        setAvailableTimes([])
        toast.error("Lütfen önce bir hizmet seçin.")
        return
      }

      // Generate available time slots based on barber's working hours
      const timeSlots = []

      // Working hours already parsed above

      const [openHour, openMinute] = openTime.split(':').map(Number)
      const [closeHour, closeMinute] = closeTime.split(':').map(Number)

      // Generate time slots at 30-minute intervals within working hours
      if (crossesMidnight) {
        // Working hours cross midnight (e.g., 08:00-02:00)
        // First generate slots from open time to 23:59
        let currentHour = openHour
        let currentMinute = openMinute

        while (currentHour < 24) {
          const timeSlot = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
          timeSlots.push(timeSlot)

          currentMinute += 30
          if (currentMinute >= 60) {
            currentHour += 1
            currentMinute = 0
          }
        }

        // Then generate slots from 00:00 to close time (next day)
        currentHour = 0
        currentMinute = 0

        while (
          currentHour < closeHour ||
          (currentHour === closeHour && currentMinute < closeMinute)
        ) {
          const timeSlot = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
          timeSlots.push(timeSlot)

          currentMinute += 30
          if (currentMinute >= 60) {
            currentHour += 1
            currentMinute = 0
          }
        }
      } else {
        // Normal working hours within same day
        let currentHour = openHour
        let currentMinute = openMinute

        while (
          currentHour < closeHour ||
          (currentHour === closeHour && currentMinute < closeMinute)
        ) {
          const timeSlot = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
          timeSlots.push(timeSlot)

          // Move to next 30-minute slot
          currentMinute += 30
          if (currentMinute >= 60) {
            currentHour += 1
            currentMinute = 0
          }
        }
      }

      // Filter out times that conflict with existing appointments
      const availableSlots = timeSlots.filter(timeSlot => {
        // We already checked for selectedService above

        // Determine which day this time slot belongs to
        const timeSlotHour = parseInt(timeSlot.split(':')[0])
        const closeHour = parseInt(closeTime.split(':')[0])
        // Next day slots are those <= close hour when crossing midnight (e.g., if close is 02:00, then 00:00, 01:00, 02:00 are next day)
        const isNextDaySlot = crossesMidnight && timeSlotHour <= closeHour

        // Use appropriate date for parsing
        const slotDate = isNextDaySlot ? new Date(date.getTime() + 24 * 60 * 60 * 1000) : date

        const startTime = parse(timeSlot, "HH:mm", slotDate)
        const endTime = addMinutes(startTime, selectedService.duration)

        // Parse close time - if crossing midnight and this is a next day slot, use next day
        let closeTimeDate
        if (crossesMidnight) {
          if (isNextDaySlot) {
            closeTimeDate = parse(closeTime, "HH:mm", new Date(date.getTime() + 24 * 60 * 60 * 1000))
          } else {
            // For same day slots when crossing midnight, close time is 23:59
            closeTimeDate = parse("23:59", "HH:mm", date)
          }
        } else {
          closeTimeDate = parse(closeTime, "HH:mm", date)
        }

        // Skip if the appointment would end after closing time
        if (isAfter(endTime, closeTimeDate)) return false

        // Check for lunch break if applicable
        if (workingHoursData.has_lunch_break && workingHoursData.lunch_start_time && workingHoursData.lunch_end_time) {
          const lunchStart = workingHoursData.lunch_start_time.substring(0, 5)
          const lunchEnd = workingHoursData.lunch_end_time.substring(0, 5)

          // Lunch break is typically on the same day, but use slot date for consistency
          const lunchStartDate = parse(lunchStart, "HH:mm", slotDate)
          const lunchEndDate = parse(lunchEnd, "HH:mm", slotDate)

          // Skip if appointment overlaps with lunch break
          // Allow exact boundary matches (appointment can start when lunch ends or end when lunch starts)
          const lunchConflict = isBefore(startTime, lunchEndDate) && isAfter(endTime, lunchStartDate)

          if (lunchConflict) {
            return false
          }
        }


        // Check for conflicts with existing appointments
        for (const appointment of (existingAppointments || [])) {
          const appointmentStart = appointment.start_time.substring(0, 5)
          const appointmentEnd = appointment.end_time.substring(0, 5)

          // Determine which date this appointment belongs to
          const appointmentDate = new Date(appointment.date)
          const appointmentDateStr = format(appointmentDate, "yyyy-MM-dd")
          const currentDateStr = format(date, "yyyy-MM-dd")
          const nextDateStr = format(new Date(date.getTime() + 24 * 60 * 60 * 1000), "yyyy-MM-dd")

          // Parse appointment times with correct date
          let appointmentStartDate, appointmentEndDate

          if (appointmentDateStr === currentDateStr) {
            // Appointment is on current day
            appointmentStartDate = parse(appointmentStart, "HH:mm", date)
            appointmentEndDate = parse(appointmentEnd, "HH:mm", date)

            // If appointment end time is before start time, it crosses midnight to next day
            if (appointmentEnd < appointmentStart) {
              appointmentEndDate = parse(appointmentEnd, "HH:mm", new Date(date.getTime() + 24 * 60 * 60 * 1000))
            }
          } else if (appointmentDateStr === nextDateStr) {
            // Appointment is on next day
            const nextDay = new Date(date.getTime() + 24 * 60 * 60 * 1000)
            appointmentStartDate = parse(appointmentStart, "HH:mm", nextDay)
            appointmentEndDate = parse(appointmentEnd, "HH:mm", nextDay)

            // If appointment end time is before start time, it crosses midnight to day after next
            if (appointmentEnd < appointmentStart) {
              appointmentEndDate = parse(appointmentEnd, "HH:mm", new Date(nextDay.getTime() + 24 * 60 * 60 * 1000))
            }
          } else {
            // Skip appointments that are not relevant to current or next day
            continue
          }

          // Check for overlap - but allow exact boundary matches
          // New appointment: startTime -> endTime
          // Existing appointment: appointmentStartDate -> appointmentEndDate
          // Conflict if: newStart < existingEnd AND newEnd > existingStart
          // But we allow: newStart == existingEnd OR newEnd == existingStart
          const hasConflict = isBefore(startTime, appointmentEndDate) && isAfter(endTime, appointmentStartDate)

          if (hasConflict) {
            return false
          }
        }

        return true
      })

      setAvailableTimes(availableSlots)
      console.log("loadAvailableTimes - availableSlots:", availableSlots)

      // If no available slots, show a message
      if (availableSlots.length === 0) {
        toast.error("Seçilen tarih ve berber için müsait saat bulunmamaktadır. Lütfen başka bir tarih veya berber seçin.")
      }
    } catch (error) {
      console.error("Error loading available times:", error)
      toast.error("Müsait saatler yüklenirken bir hata oluştu.")
    }
  }

  // Handle form submission
  const onSubmit = async () => {
    setIsLoading(true)

    try {
      const values = form.getValues()

      if (!selectedDate) {
        toast.error("Lütfen bir tarih seçin.")
        setIsLoading(false)
        return
      }



      if (!selectedService) {
        toast.error("Lütfen bir hizmet seçin.")
        setIsLoading(false)
        return
      }

      if (!values.start_time) {
        toast.error("Lütfen bir saat seçin.")
        setIsLoading(false)
        return
      }

      // Get working hours to determine if they cross midnight
      const workingHoursData = barberWorkingHoursList.find(wh =>
        wh.barber_id === selectedBarber && wh.day_of_week === selectedDate.getDay()
      )

      if (!workingHoursData) {
        toast.error("Berber çalışma saatleri bulunamadı.")
        setIsLoading(false)
        return
      }

      const openTime = workingHoursData.open_time.substring(0, 5)
      const closeTime = workingHoursData.close_time.substring(0, 5)
      const crossesMidnight = closeTime < openTime

      // Determine which date this appointment should be created on
      const timeSlotHour = parseInt(values.start_time.split(':')[0])
      const closeHour = parseInt(closeTime.split(':')[0])
      // Next day slots are those <= close hour when crossing midnight (e.g., if close is 02:00, then 00:00, 01:00, 02:00 are next day)
      const isNextDaySlot = crossesMidnight && timeSlotHour <= closeHour

      // Use appropriate date for the appointment
      const appointmentDate = isNextDaySlot ? new Date(selectedDate.getTime() + 24 * 60 * 60 * 1000) : selectedDate
      const appointmentFormattedDate = format(appointmentDate, "yyyy-MM-dd")

      // Calculate end time
      const startTime = parse(values.start_time, "HH:mm", appointmentDate)
      const endTime = addMinutes(startTime, selectedService.duration)
      const endTimeString = format(endTime, "HH:mm")

      // Create appointment
      const newAppointmentId = await publicAccess.createPublicAppointment(
        salonId,
        selectedBarber as string,
        selectedService.id,
        appointmentFormattedDate,
        values.start_time,
        endTimeString,
        values.name + ' ' + values.surname,
        values.phone,
        values.email || ''
      )
      toast.success("Randevu başarıyla oluşturuldu!")

      // Send Telegram notification for new appointment (non-blocking)
      try {
        const selectedBarberData = barbersList.find(b => b.id === selectedBarber)

        if (selectedBarberData && selectedService && newAppointmentId) {
          const notificationData = formatAppointmentForNotification(
            {
              id: newAppointmentId,
              salon_id: salonId,
              salon_name: '', // salon name will be fetched in the API
              fullname: values.name + ' ' + values.surname,
              phonenumber: values.phone,
              email: values.email || '',
              barber_name: selectedBarberData.name,
              service_name: selectedService.name,
              date: appointmentFormattedDate,
              start_time: values.start_time,
              end_time: endTimeString,
              status: 'booked',
              notes: ''
            },
            '', // salon name will be fetched in the API
            selectedBarberData.name,
            selectedService.name
          )

          notifyNewAppointment(notificationData)
        }
      } catch (error) {
        console.error('Failed to send Telegram notification for new appointment:', error)
        // Don't show error to user as this is a background operation
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error creating appointment:", error)
      toast.error("Randevu oluşturulurken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  // Navigate to previous step
  const goToPreviousStep = () => {
    switch (currentStep) {
      case "barber":
        setCurrentStep("service")
        break
      case "date":
        setCurrentStep("barber")
        break
      case "time":
        setCurrentStep("date")
        break
      case "info":
        setCurrentStep("time")
        break
      case "summary":
        setCurrentStep("info")
        break
      default:
        break
    }
  }

  return (
    <Form {...form}>
      <div className="flex flex-col min-h-[600px]">
        <StepIndicator
          currentStep={currentStep}
          steps={["service", "barber", "date", "time", "info", "summary"]}
        />

        <div className="flex-1 p-6 sm:p-8 bg-gradient-to-b from-background to-muted/20">
          {currentStep === "service" && (
            <ServiceSelector
              services={servicesList}
              selectedServiceId={form.getValues("service_id")}
              onSelect={handleServiceChange}
            />
          )}

          {currentStep === "barber" && (
            <BarberSelector
              barbers={barbersList}
              selectedBarberId={form.getValues("barber_id")}
              onSelect={handleBarberChange}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "date" && (
            <DateSelector
              selectedDate={selectedDate}
              onSelect={handleDateChange}
              disabledDates={disabledDates}
              holidayDates={holidayDates}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "time" && (
            <TimeSelector
              date={selectedDate}
              availableTimes={availableTimes}
              selectedTime={form.getValues("start_time")}
              onSelect={handleTimeChange}
              barberWorkingHours={barberWorkingHoursList}
              existingAppointmentTimes={existingAppointmentTimes}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "info" && (
            <CustomerInfoForm
              defaultValues={{
                name: form.getValues("name"),
                surname: form.getValues("surname"),
                phone: form.getValues("phone"),
                email: form.getValues("email") || ""
              }}
              onSubmit={handleCustomerInfoSubmit}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "summary" && (
            <BookingSummary
              service={selectedService}
              barber={barbersList.find(b => b.id === selectedBarber)}
              date={selectedDate}
              time={form.getValues("start_time") || ""}
              customerInfo={{
                name: form.getValues("name"),
                surname: form.getValues("surname"),
                phone: form.getValues("phone"),
                email: form.getValues("email") || ""
              }}
              onSubmit={onSubmit}
              onBack={goToPreviousStep}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>
    </Form>
  )
}
