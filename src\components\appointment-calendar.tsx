"use client"

import { useState, useEffect, use<PERSON>allback, useRef } from "react"
import {
  Calendar as CalendarIcon,
  List,
  Calendar as CalendarViewIcon, // Renamed to avoid conflict with Calendar component
  LayoutGrid,
  X,
  Filter,
  Search,
  ChevronLeft,
  ChevronRight,
  CalendarDays
} from "lucide-react"
import { format, startOfWeek, endOfWeek, addDays, startOfMonth, endOfMonth, parseISO, isEqual, differenceInDays } from "date-fns" // isEqual, differenceInDays added
import { tr } from "date-fns/locale"
import { DateRange } from "react-day-picker"

import { cn, debounce } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { WeeklyCalendarView } from "@/components/calendar/WeeklyCalendarView"
import { DailyCalendarView } from "@/components/calendar/DailyCalendarView"
import { MonthlyCalendarView } from "@/components/calendar/MonthlyCalendarView"
import { CustomRangeCalendarView } from "@/components/calendar/CustomRangeCalendarView"
import { AppointmentCreateModal } from "@/components/calendar/AppointmentCreateModal"
import { appointments as appointmentService, barbers as barberService, holidays as holidayService, barberWorkingHours as barberWorkingHourService, services as serviceService } from "@/lib/db" // aliased for clarity
import { Appointment, Barber, Holiday, BarberWorkingHours, Service } from "@/lib/db/types"
import { HolidayDate } from "@/components/ui/holiday-calendar"
import { AppointmentsOnlySkeleton } from "@/components/ui/skeleton-loaders"

import { supabaseClient } from '@/lib/supabase-singleton';
const supabase = supabaseClient;


interface AppointmentCalendarProps {
  salonId: string
}

type CalendarViewType = "daily" | "weekly" | "monthly"

export function AppointmentCalendar({ salonId }: AppointmentCalendarProps) {
  const [viewType, setViewType] = useState<CalendarViewType>("weekly")
  const [currentDate, setCurrentDate] = useState<Date>(new Date()) // Renamed 'date' to 'currentDate' for clarity
  const [dateRange, setDateRange] = useState<DateRange>({ from: undefined, to: undefined })
  const [isCustomRangeView, setIsCustomRangeView] = useState<boolean>(false) // Renamed 'isCustomRange'
  
  const [allAppointments, setAllAppointments] = useState<Appointment[]>([])
  const [isLoadingAppointments, setIsLoadingAppointments] = useState(true) // More specific loading state
  const [isMobile, setIsMobile] = useState(false)

  const [barbersList, setBarbersList] = useState<Barber[]>([])
  const [selectedBarberId, setSelectedBarberId] = useState<string | null>(null) // Renamed
  const [isLoadingBarbers, setIsLoadingBarbers] = useState(true)

  const [servicesList, setServicesList] = useState<Service[]>([])
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(null) // Renamed
  const [isLoadingServices, setIsLoadingServices] = useState(true)

  const [selectedStatus, setSelectedStatus] = useState<string | null>(null)
  const statusOptions = [
    { value: "booked", label: "Aktif" },
    { value: "completed", label: "Tamamlandı" },
    { value: "cancelled", label: "İptal Edildi" },
    { value: "no-show", label: "Gelmedi" }
  ]

  const [searchTerm, setSearchTerm] = useState<string>("")
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("")

  const [holidaysList, setHolidaysList] = useState<Holiday[]>([])
  const [holidayDates, setHolidayDates] = useState<HolidayDate[]>([])
  const [isLoadingHolidays, setIsLoadingHolidays] = useState(true)

  const [barberWorkingHoursList, setBarberWorkingHoursList] = useState<BarberWorkingHours[]>([])
  const [isLoadingBarberWorkingHours, setIsLoadingBarberWorkingHours] = useState(true)

  const [activeFiltersCount, setActiveFiltersCount] = useState<number>(0)

  // Derived date ranges (calculated in one place)
  const [currentDisplayRange, setCurrentDisplayRange] = useState<{ start: Date, end: Date, text: string }>({
    start: startOfWeek(new Date(), { weekStartsOn: 1 }),
    end: endOfWeek(new Date(), { weekStartsOn: 1 }),
    text: ""
  });

  useEffect(() => {
    let start: Date, end: Date, text: string;
    if (isCustomRangeView && dateRange.from && dateRange.to) {
      start = dateRange.from;
      end = dateRange.to;
      text = `${format(start, "d MMMM", { locale: tr })} - ${format(end, "d MMMM yyyy", { locale: tr })}`;
    } else if (viewType === "weekly") {
      start = startOfWeek(currentDate, { weekStartsOn: 1 });
      end = endOfWeek(currentDate, { weekStartsOn: 1 });
      text = `${format(start, "d MMMM", { locale: tr })} - ${format(end, "d MMMM yyyy", { locale: tr })}`;
    } else if (viewType === "monthly") {
      start = startOfMonth(currentDate);
      end = endOfMonth(currentDate);
      text = format(currentDate, "MMMM yyyy", { locale: tr });
    } else { // daily
      start = currentDate;
      end = currentDate;
      text = format(currentDate, "d MMMM yyyy, EEEE", { locale: tr });
    }
    setCurrentDisplayRange({ start, end, text });
  }, [currentDate, viewType, isCustomRangeView, dateRange]);


  const [weekDays, setWeekDays] = useState<Date[]>([])
  const [appointmentsByDay, setAppointmentsByDay] = useState<{ date: Date, appointments: Appointment[] }[]>([])

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [selectedSlotDate, setSelectedSlotDate] = useState<Date>(new Date())
  const [selectedSlotTime, setSelectedSlotTime] = useState<string | undefined>(undefined)

  const [isFilterPopoverOpen, setIsFilterPopoverOpen] = useState(false) // Renamed
  const [isCalendarPopoverOpen, setIsCalendarPopoverOpen] = useState(false) // Renamed

  // Debounce search term
  useEffect(() => {
    const handler = debounce(() => setDebouncedSearchTerm(searchTerm), 300)
    handler()
    return () => {
      // If using a debounce that returns a cancel function:
      // handler.cancel(); 
      // For basic setTimeout, clearTimeout is handled if searchTerm changes rapidly by re-running effect
    };
  }, [searchTerm])


  const loadBarbers = useCallback(async () => {
    if (!salonId) {
      setBarbersList([]);
      setIsLoadingBarbers(false);
      return;
    }
    setIsLoadingBarbers(true);
    try {
      const data = await barberService.getBarbers(salonId);
      setBarbersList(data);
    } catch (error) {
      console.error("Error loading barbers:", error);
      setBarbersList([]);
    } finally {
      setIsLoadingBarbers(false);
    }
  }, [salonId]);

  const loadServices = useCallback(async () => {
    if (!salonId) {
      setServicesList([]);
      setIsLoadingServices(false);
      return;
    }
    setIsLoadingServices(true);
    try {
      const data = await serviceService.getServices(salonId);
      setServicesList(data);
    } catch (error) {
      console.error("Error loading services:", error);
      setServicesList([]);
    } finally {
      setIsLoadingServices(false);
    }
  }, [salonId]);

  const loadHolidays = useCallback(async () => {
    if (!salonId) return;
    setIsLoadingHolidays(true);
    try {
      const startDateStr = format(currentDisplayRange.start, "yyyy-MM-dd");
      const endDateStr = format(currentDisplayRange.end, "yyyy-MM-dd");
      
      const data = await holidayService.getHolidaysInRange(salonId, startDateStr, endDateStr);
      setHolidaysList(data);
      const holidayDateObjects = data.map(holiday => ({
        date: parseISO(holiday.date), // Ensure date is parsed correctly
        description: holiday.description || "Tatil Günü"
      }));
      setHolidayDates(holidayDateObjects);
    } catch (error) {
      console.error("Error loading holidays:", error);
      setHolidaysList([]);
      setHolidayDates([]);
    } finally {
      setIsLoadingHolidays(false);
    }
  }, [salonId, currentDisplayRange.start, currentDisplayRange.end]); // Depends on derived range

  const loadBarberWorkingHours = useCallback(async () => {
    if (!salonId || !selectedBarberId) {
        setBarberWorkingHoursList([]); // Clear if no barber selected
        return;
    }
    setIsLoadingBarberWorkingHours(true);
    try {
      const data = await barberWorkingHourService.getBarberWorkingHours(selectedBarberId);
      setBarberWorkingHoursList(data);
    } catch (error) {
      console.error("Error loading barber working hours:", error);
      setBarberWorkingHoursList([]);
    } finally {
      setIsLoadingBarberWorkingHours(false);
    }
  }, [salonId, selectedBarberId]);

  useEffect(() => { loadBarbers(); }, [loadBarbers]);
  useEffect(() => { loadServices(); }, [loadServices]);
  useEffect(() => { loadHolidays(); }, [loadHolidays]);
  useEffect(() => { 
    if (selectedBarberId) loadBarberWorkingHours();
    else setBarberWorkingHoursList([]); // Clear if barber is deselected
  }, [selectedBarberId, loadBarberWorkingHours]);


  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile && viewType === "monthly") {
        setViewType("weekly");
      }
    };
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [viewType]); // viewType dependency to re-evaluate if it changes


  const loadAppointments = useCallback(async () => {
    console.log("loadAppointments CALLED. Filters:", { selectedBarberId, selectedServiceId, selectedStatus, debouncedSearchTerm });
    // Prevent loading if custom range is being selected but not complete
    if (isCustomRangeView && dateRange.from && !dateRange.to) {
      console.log("loadAppointments SKIPPED: custom range selection in progress.");
      return;
    }
    if (!salonId) {
      console.log("loadAppointments SKIPPED: No salon ID.");
      setAllAppointments([]);
      setIsLoadingAppointments(false);
      return;
    }

    setIsLoadingAppointments(true);
    try {
      const startDateStr = format(currentDisplayRange.start, "yyyy-MM-dd");
      const endDateStr = format(currentDisplayRange.end, "yyyy-MM-dd");

      console.log(`Fetching appointments for ${salonId} from ${startDateStr} to ${endDateStr}`);
      const data = await appointmentService.getAppointmentsInRange(salonId, startDateStr, endDateStr);
      
      let filteredAppointments = data as Appointment[];

      if (selectedBarberId) {
        filteredAppointments = filteredAppointments.filter(apt => apt.barber_id === selectedBarberId);
      }
      if (selectedServiceId) {
        filteredAppointments = filteredAppointments.filter(apt => apt.service_id === selectedServiceId);
      }
      if (selectedStatus) {
        filteredAppointments = filteredAppointments.filter(apt => apt.status === selectedStatus);
      }
      if (debouncedSearchTerm.trim()) {
        const searchTermLower = debouncedSearchTerm.toLowerCase().trim();
        filteredAppointments = filteredAppointments.filter(apt =>
          (apt.fullname?.toLowerCase() || '').includes(searchTermLower) ||
          (apt.phonenumber?.toLowerCase() || '').includes(searchTermLower) ||
          (apt.email?.toLowerCase() || '').includes(searchTermLower) ||
          (apt.notes?.toLowerCase() || '').includes(searchTermLower) ||
          (apt.barbers?.name?.toLowerCase() || '').includes(searchTermLower) ||
          (apt.services?.name?.toLowerCase() || '').includes(searchTermLower)
        );
      }

      let count = 0;
      if (selectedBarberId) count++;
      if (selectedServiceId) count++;
      if (selectedStatus) count++;
      if (debouncedSearchTerm.trim()) count++;
      setActiveFiltersCount(count);

      setAllAppointments(filteredAppointments);
    } catch (error) {
      console.error("Error loading appointments:", error);
      setAllAppointments([]);
    } finally {
      setIsLoadingAppointments(false);
    }
  }, [
    salonId, 
    currentDisplayRange.start, // Use derived range start/end
    currentDisplayRange.end,
    selectedBarberId, 
    selectedServiceId, 
    selectedStatus, 
    debouncedSearchTerm,
    isCustomRangeView, // This is needed to decide if currentDisplayRange is from dateRange
    dateRange.from, // Still need these for the "incomplete custom range" check
    dateRange.to
  ]);

  // This is the main effect that triggers loading appointments when relevant dependencies change.
  useEffect(() => {
    // Ensure date objects are truly different before triggering load
    // This check might be overly cautious if currentDisplayRange effect already handles stability
    // but can be useful.
    // For objects/dates in dependency arrays, stability of reference is key.
    // currentDisplayRange.start and .end are new Date objects from its own effect.
    // So this useEffect will run when they change.
    loadAppointments();
  }, [loadAppointments]); // The dependency is the memoized loadAppointments function itself.

  // Store loadAppointments in a ref for Supabase to avoid re-subscribing too often
  const loadAppointmentsRef = useRef(loadAppointments);
  useEffect(() => {
    loadAppointmentsRef.current = loadAppointments;
  }, [loadAppointments]);

  useEffect(() => {
    if (!salonId) return;

    const channel = supabase.channel('appointments-calendar-view-new'); // Use a unique channel name

    const handleChange = (payload: any) => {
      console.log('Supabase change detected:', payload.eventType, payload.new || payload.old);
      // Optional: Add logic here to check if the change is relevant to the current view/filters
      // For now, just reload.
      loadAppointmentsRef.current();
    };
    
    const appointmentsSubscription = channel
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'appointments', filter: `salon_id=eq.${salonId}` },
        handleChange
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'appointments',
          filter: `salon_id=eq.${salonId}`,
        },
        handleChange
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'appointments',
          filter: `salon_id=eq.${salonId}`,
        },
        handleChange
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'appointments',
          filter: `salon_id=eq.${salonId}`,
        },
        handleChange
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to Supabase appointments changes for salon:', salonId);
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
          console.error('Supabase subscription error/status:', status, err);
          // Potentially attempt to resubscribe or notify user
        }
      });

    return () => {
      console.log('Cleaning up Supabase subscription for salon:', salonId);
      supabase.removeChannel(appointmentsSubscription).catch(err => console.error("Error removing channel", err));
    };
  }, [salonId]); // Only re-subscribe if salonId changes. loadAppointmentsRef handles the callback.


  useEffect(() => {
    if (viewType === "weekly") {
      const start = currentDisplayRange.start; // Already calculated
      setWeekDays(Array.from({ length: 7 }, (_, i) => addDays(start, i)));
    }
  }, [currentDisplayRange.start, viewType]);

  useEffect(() => {
    if (viewType === "weekly" && weekDays.length > 0) {
      const newAppointmentsByDay = weekDays.map(day => ({
        date: day,
        appointments: allAppointments.filter(apt => apt.date === format(day, "yyyy-MM-dd"))
      }));
      setAppointmentsByDay(newAppointmentsByDay);
    }
  }, [weekDays, allAppointments, viewType]);


  const goToPrevious = () => {
    if (viewType === "weekly") {
      setCurrentDate(prev => addDays(prev, -7));
    } else if (viewType === "monthly") {
      setCurrentDate(prev => {
        const newDate = new Date(prev);
        newDate.setMonth(prev.getMonth() - 1);
        return newDate;
      });
    } else { // Daily
      setCurrentDate(prev => addDays(prev, -1));
    }
    setIsCustomRangeView(false); // Exit custom range if navigating
  };

  const goToNext = () => {
    if (viewType === "weekly") {
      setCurrentDate(prev => addDays(prev, 7));
    } else if (viewType === "monthly") {
      setCurrentDate(prev => {
        const newDate = new Date(prev);
        newDate.setMonth(prev.getMonth() + 1);
        return newDate;
      });
    } else { // Daily
      setCurrentDate(prev => addDays(prev, 1));
    }
    setIsCustomRangeView(false); // Exit custom range if navigating
  };

  const goToToday = () => {
    setCurrentDate(new Date());
    setIsCustomRangeView(false); // Exit custom range if navigating
  };

  const handleSlotClick = (slotDate: Date, slotTime?: string) => {
    setSelectedSlotDate(slotDate);
    setSelectedSlotTime(slotTime);
    setIsCreateModalOpen(true);
  };

  const handleAppointmentCreated = () => {
    // The Supabase real-time subscription should ideally pick this up.
    // However, calling loadAppointments() directly ensures faster UI update
    // if there's any latency or if subscription is momentarily disconnected.
    // If Supabase is reliable, this explicit call might be redundant.
    // Test to see if realtime updates are sufficient.
    loadAppointments(); 
  };
  
  const clearAllFilters = () => {
    setSelectedBarberId(null);
    setSelectedServiceId(null);
    setSelectedStatus(null);
    setSearchTerm(""); // This will trigger debouncedSearchTerm update
  };

  // Loading state for the main content area
  if (isLoadingAppointments && allAppointments.length === 0) { // Show skeleton only on initial load or full data change
    return (
      <div className="space-y-4">
        {/* Header part (non-skeleton) */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold">{currentDisplayRange.text}</h2>
            <p className="text-muted-foreground">Randevu takvimi</p>
          </div>
          {/* Controls (non-skeleton) */}
           <div className="flex flex-wrap items-center gap-2">
            <Tabs value={viewType} onValueChange={(value) => {setViewType(value as CalendarViewType); setIsCustomRangeView(false);}} className="mr-2 overflow-hidden">
              <TabsList className="h-9 p-0.5 bg-muted/60">
                 <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <TabsTrigger
                        value="daily"
                        className="flex items-center justify-center w-8 h-8 p-1 relative data-[state=active]:bg-background data-[state=active]:shadow-sm z-10"
                      >
                        <List className="h-4 w-4" />
                      </TabsTrigger>
                    </TooltipTrigger>
                    <TooltipContent>Günlük Görünüm</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <TabsTrigger
                        value="weekly"
                        className="flex items-center justify-center w-8 h-8 p-1 relative data-[state=active]:bg-background data-[state=active]:shadow-sm z-10"
                      >
                        <CalendarViewIcon className="h-4 w-4" />
                      </TabsTrigger>
                    </TooltipTrigger>
                    <TooltipContent>Haftalık Görünüm</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {!isMobile && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <TabsTrigger
                        value="monthly"
                        className="flex items-center justify-center w-8 h-8 p-1 relative data-[state=active]:bg-background data-[state=active]:shadow-sm z-10"
                      >
                        <LayoutGrid className="h-4 w-4" />
                      </TabsTrigger>
                    </TooltipTrigger>
                    <TooltipContent>Aylık Görünüm</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                )}
              </TabsList>
            </Tabs>

            <Popover open={isFilterPopoverOpen} onOpenChange={setIsFilterPopoverOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" size="icon" className="relative w-8 h-8 p-1 z-10">
                  <Filter className="h-4 w-4" />
                  {activeFiltersCount > 0 && (
                    <Badge variant="secondary" className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 text-[10px] flex items-center justify-center">
                      {activeFiltersCount}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4"> {/* Filter content here */} </PopoverContent>
            </Popover>
            
            {/* Navigation Buttons */}
            <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={goToPrevious} className="w-8 h-8 p-1 z-10"><ChevronLeft className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent>{viewType === "daily" ? "Önceki Gün" : viewType === "weekly" ? "Önceki Hafta" : "Önceki Ay"}</TooltipContent></Tooltip></TooltipProvider>
            <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={goToToday} className="w-8 h-8 p-1 z-10"><CalendarDays className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent>Bugün</TooltipContent></Tooltip></TooltipProvider>
            <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={goToNext} className="w-8 h-8 p-1 z-10"><ChevronRight className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent>{viewType === "daily" ? "Sonraki Gün" : viewType === "weekly" ? "Sonraki Hafta" : "Sonraki Ay"}</TooltipContent></Tooltip></TooltipProvider>
            
            <Popover open={isCalendarPopoverOpen} onOpenChange={setIsCalendarPopoverOpen}>
              <PopoverTrigger asChild>
                 <Button variant="outline" size="icon" className="ml-auto w-8 h-8 p-1 z-10">
                    <CalendarIcon className="h-4 w-4" />
                  </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0"> {/* Calendar Popover content here */} </PopoverContent>
            </Popover>
          </div>
        </div>
        <AppointmentsOnlySkeleton viewType={viewType} />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">{currentDisplayRange.text}</h2>
          <p className="text-muted-foreground">Randevu takvimi</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          {/* View Type Tabs */}
          <Tabs value={viewType} onValueChange={(value) => {
            setViewType(value as CalendarViewType);
            setIsCustomRangeView(false); // Changing view type exits custom range
          }} className="mr-2 overflow-hidden">
            <TabsList className="h-9 p-0.5 bg-muted/60">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="daily" className="flex items-center justify-center w-8 h-8 p-1 relative data-[state=active]:bg-background data-[state=active]:shadow-sm z-10">
                      <List className="h-4 w-4" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>Günlük Görünüm</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="weekly" className="flex items-center justify-center w-8 h-8 p-1 relative data-[state=active]:bg-background data-[state=active]:shadow-sm z-10">
                      <CalendarViewIcon className="h-4 w-4" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>Haftalık Görünüm</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {!isMobile && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <TabsTrigger value="monthly" className="flex items-center justify-center w-8 h-8 p-1 relative data-[state=active]:bg-background data-[state=active]:shadow-sm z-10">
                        <LayoutGrid className="h-4 w-4" />
                      </TabsTrigger>
                    </TooltipTrigger>
                    <TooltipContent>Aylık Görünüm</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </TabsList>
          </Tabs>

          {/* Filter Popover */}
          <Popover open={isFilterPopoverOpen} onOpenChange={setIsFilterPopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon" className="relative w-8 h-8 p-1 z-10" aria-label="Filtreler">
                <Filter className="h-4 w-4" />
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 text-[10px] flex items-center justify-center">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4">
              <div className="space-y-4">
                <h4 className="font-medium">Filtreler</h4>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Arama</label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Müşteri, berber, hizmet ara..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-8"/>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Berber</label>
                  <Select value={selectedBarberId || "all"} onValueChange={(value) => setSelectedBarberId(value === "all" ? null : value)} disabled={isLoadingBarbers}>
                    <SelectTrigger className="w-full"><SelectValue placeholder="Tüm Berberler" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tüm Berberler</SelectItem>
                      {barbersList.map((barber) => (<SelectItem key={barber.id} value={barber.id}>{barber.name}</SelectItem>))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Hizmet</label>
                  <Select value={selectedServiceId || "all"} onValueChange={(value) => setSelectedServiceId(value === "all" ? null : value)} disabled={isLoadingServices}>
                    <SelectTrigger className="w-full"><SelectValue placeholder="Tüm Hizmetler" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tüm Hizmetler</SelectItem>
                      {servicesList.map((service) => (<SelectItem key={service.id} value={service.id}>{service.name}</SelectItem>))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Durum</label>
                  <Select value={selectedStatus || "all"} onValueChange={(value) => setSelectedStatus(value === "all" ? null : value)}>
                    <SelectTrigger className="w-full"><SelectValue placeholder="Tüm Durumlar" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tüm Durumlar</SelectItem>
                      {statusOptions.map((status) => (<SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>))}
                    </SelectContent>
                  </Select>
                </div>
                {activeFiltersCount > 0 && (
                  <Button variant="outline" size="sm" className="w-full" onClick={clearAllFilters}>
                    <X className="mr-2 h-4 w-4" /> Filtreleri Temizle
                  </Button>
                )}
              </div>
            </PopoverContent>
          </Popover>

          {/* Navigation Buttons */}
          <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={goToPrevious} className="w-8 h-8 p-1 z-10"><ChevronLeft className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent>{viewType === "daily" ? "Önceki Gün" : viewType === "weekly" ? "Önceki Hafta" : "Önceki Ay"}</TooltipContent></Tooltip></TooltipProvider>
          <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={goToToday} className="w-8 h-8 p-1 z-10"><CalendarDays className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent>Bugün</TooltipContent></Tooltip></TooltipProvider>
          <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={goToNext} className="w-8 h-8 p-1 z-10"><ChevronRight className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent>{viewType === "daily" ? "Sonraki Gün" : viewType === "weekly" ? "Sonraki Hafta" : "Sonraki Ay"}</TooltipContent></Tooltip></TooltipProvider>
          
          {/* Calendar Popover for Date Selection */}
          <Popover
            open={isCalendarPopoverOpen}
            onOpenChange={(open) => {
              // Prevent closing if selecting the first date in a range
              if (!open && isCustomRangeView && dateRange.from && !dateRange.to) {
                return;
              }
              setIsCalendarPopoverOpen(open);
            }}
          >
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon" className="ml-auto w-8 h-8 p-1 z-10">
                <CalendarIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Tabs defaultValue={isCustomRangeView ? "range" : "single"} 
                    onValueChange={(tabValue) => {
                        if (tabValue === "range") setIsCustomRangeView(true);
                        // No need to set isCustomRangeView to false for "single" here,
                        // as selecting a single date will do that.
                    }}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="single">Tek Gün</TabsTrigger>
                  <TabsTrigger value="range">Tarih Aralığı</TabsTrigger>
                </TabsList>
                {isCustomRangeView && dateRange.from && (
                  <div className="p-3 border-b">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => {
                        setIsCustomRangeView(false);
                        setDateRange({ from: undefined, to: undefined });
                        // Optionally, set currentDate to dateRange.from or today
                        if (dateRange.from) setCurrentDate(dateRange.from); else goToToday();
                        setIsCalendarPopoverOpen(false);
                      }}
                    >
                      <X className="mr-2 h-4 w-4" />
                      Tarih Aralığını Temizle
                    </Button>
                  </div>
                )}
                <TabsContent value="single" className="p-0"> {/* p-0 if calendar has its own padding */}
                  <Calendar
                    mode="single"
                    selected={isCustomRangeView ? undefined : currentDate} // Show selected only if not in custom range mode from tab
                    onSelect={(day) => {
                      if (day) {
                        setCurrentDate(day);
                        setIsCustomRangeView(false); // Explicitly switch to single day view logic
                        setDateRange({ from: undefined, to: undefined }); // Clear any partial range
                        setIsCalendarPopoverOpen(false);
                      }
                    }}
                    initialFocus
                    locale={tr}
                  />
                </TabsContent>
                <TabsContent value="range" className="p-0">
                  <Calendar
                    mode="range"
                    selected={dateRange}
                    onSelect={(range) => {
                      if (range) {
                        setDateRange(range);
                        setIsCustomRangeView(true); // Ensure we are in custom range mode
                        if (range.from && range.to) {
                            // Optional: set currentDate to the start of the range
                            setCurrentDate(range.from);
                            setIsCalendarPopoverOpen(false);
                        }
                      }
                    }}
                    numberOfMonths={isMobile ? 1 : 2}
                    initialFocus
                    locale={tr}
                    disabled={(date) => 
                        dateRange.from && !dateRange.to && differenceInDays(date, dateRange.from) < 0
                        // Example: disable past dates if 'from' is selected for 'to'
                    }
                  />
                </TabsContent>
              </Tabs>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Calendar Views */}
      {isLoadingAppointments && <AppointmentsOnlySkeleton viewType={isCustomRangeView ? "custom" : viewType} />}
      {!isLoadingAppointments && (
        <>
            {!isCustomRangeView && viewType === "weekly" && (
                <WeeklyCalendarView
                weekDays={weekDays}
                appointmentsByDay={appointmentsByDay}
                isLoading={isLoadingAppointments} // Pass specific loading state
                onSlotClick={handleSlotClick}
                holidayDates={holidayDates}
                barberWorkingHours={barberWorkingHoursList}
                onAppointmentUpdated={handleAppointmentCreated} // Or a more specific handler if needed
                />
            )}
            {!isCustomRangeView && viewType === "daily" && (
                <DailyCalendarView
                selectedDate={currentDate}
                appointments={allAppointments}
                isLoading={isLoadingAppointments}
                onSlotClick={handleSlotClick}
                holidayDates={holidayDates}
                barberWorkingHours={barberWorkingHoursList}
                onAppointmentUpdated={handleAppointmentCreated}
                />
            )}
            {!isCustomRangeView && viewType === "monthly" && !isMobile && (
                <MonthlyCalendarView
                selectedMonth={currentDate}
                appointments={allAppointments}
                isLoading={isLoadingAppointments}
                onDateClick={(date) => handleSlotClick(date)} // Ensure this passes a Date object
                holidayDates={holidayDates}
                barberWorkingHours={barberWorkingHoursList}
                onAppointmentUpdated={handleAppointmentCreated}
                />
            )}
            {isCustomRangeView && dateRange.from && dateRange.to && (
                <CustomRangeCalendarView
                startDate={dateRange.from}
                endDate={dateRange.to}
                appointments={allAppointments}
                isLoading={isLoadingAppointments}
                onDateClick={(date) => handleSlotClick(date)}
                holidayDates={holidayDates}
                barberWorkingHours={barberWorkingHoursList}
                onAppointmentUpdated={handleAppointmentCreated}
                />
            )}
        </>
      )}


      <AppointmentCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        salonId={salonId}
        selectedDate={selectedSlotDate}
        selectedTime={selectedSlotTime}
        selectedBarberId={selectedBarberId || undefined} // Pass selectedBarberId
        onSuccess={handleAppointmentCreated}
      />
    </div>
  );
}