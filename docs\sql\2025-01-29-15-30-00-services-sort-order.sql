-- Services tablosuna sort_order kolonu ekleme
-- Tarih: 2025-01-29 15:30:00
-- Açıklama: <PERSON>z<PERSON><PERSON> için sıralama sistemi ekleme

-- Services tablosuna sort_order kolonu ekle
ALTER TABLE services ADD COLUMN sort_order INTEGER DEFAULT 0;

-- Mevcut kayıtlar için sort_order değerlerini ata (created_at sırasına göre)
WITH ordered_services AS (
  SELECT id, ROW_NUMBER() OVER (PARTITION BY salon_id ORDER BY created_at) as rn
  FROM services
)
UPDATE services 
SET sort_order = ordered_services.rn
FROM ordered_services 
WHERE services.id = ordered_services.id;

-- Index ekle performans için
CREATE INDEX idx_services_salon_sort ON services(salon_id, sort_order);

-- <PERSON>ntrol sorgusu
SELECT salon_id, name, sort_order, created_at
FROM services
ORDER BY salon_id, sort_order;

-- Public RPC fonksiyonunu güncelle
DROP FUNCTION IF EXISTS get_public_services_by_salon_id(UUID);

CREATE OR REPLACE FUNCTION get_public_services_by_salon_id(p_salon_id UUID)
RETURNS TABLE(
  id UUID,
  salon_id UUID,
  name TEXT,
  description TEXT,
  duration INTEGER,
  price NUMERIC,
  sort_order INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT s.id, s.salon_id, s.name, s.description, s.duration, s.price, s.sort_order
  FROM services s
  WHERE s.salon_id = p_salon_id
  ORDER BY s.sort_order ASC;
END;
$$;
