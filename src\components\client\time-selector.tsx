"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, <PERSON>R<PERSON>, ArrowLeft, Info } from "lucide-react"
import { format, parse, addMinutes } from "date-fns"
import { tr } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { BarberWorkingHours } from "@/lib/db/types"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface TimeSlot {
  time: string;
  isAvailable: boolean;
  reason?: string;
}

interface TimeSelectorProps {
  date: Date | null
  availableTimes: string[]
  selectedTime: string | undefined
  onSelect: (time: string) => void
  barberWorkingHours?: BarberWorkingHours[]
  existingAppointmentTimes?: string[]
  onBack: () => void
}

export function TimeSelector({
  date,
  availableTimes,
  selectedTime,
  onSelect,
  barberWorkingHours = [],
  existingAppointmentTimes = [],
  onBack
}: TimeSelectorProps) {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [timeOfDay, setTimeOfDay] = useState<"morning" | "afternoon" | "evening" | "all">("all")

  // Generate time slots
  useEffect(() => {
    console.log("TimeSelector - availableTimes:", availableTimes)

    const slots: TimeSlot[] = []

    // Default start and end times
    let startTimeStr = "08:00"
    let endTimeStr = "23:59"

    // If barber working hours are available and date is selected, use them
    if (barberWorkingHours.length > 0 && date) {
      const dayOfWeek = date.getDay()
      const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

      if (workingHoursForDay && !workingHoursForDay.is_closed) {
        startTimeStr = workingHoursForDay.open_time.substring(0, 5)
        endTimeStr = workingHoursForDay.close_time.substring(0, 5)
      }
    }

    // Parse times
    let currentTime = parse(startTimeStr, "HH:mm", new Date())
    const endTime = parse(endTimeStr, "HH:mm", new Date())

    // Generate slots with 30-minute intervals
    while (currentTime <= endTime) {
      const timeString = format(currentTime, "HH:mm")

      // Check if this time slot has an existing appointment
      const hasExistingAppointment = existingAppointmentTimes.includes(timeString)

      // Determine if the time slot is available
      // If the time is already selected, it's available
      // Otherwise, if availableTimes has entries, check if this time is in the list
      // If availableTimes is empty, consider the time available only if there's no existing appointment
      const isAvailable = (selectedTime && timeString === selectedTime) ||
                         (availableTimes.length > 0 ? availableTimes.includes(timeString) : !hasExistingAppointment)

      let reason = ""

      // If not available, determine the reason
      if (!isAvailable) {
        if (hasExistingAppointment) {
          reason = "Dolu randevu"
        } else {
          reason = "Müsait değil"
        }
      }

      slots.push({
        time: timeString,
        isAvailable,
        reason
      })

      currentTime = addMinutes(currentTime, 30)
    }

    setTimeSlots(slots)
  }, [date, availableTimes, barberWorkingHours, existingAppointmentTimes, selectedTime])

  // Filter time slots based on time of day
  const filteredTimeSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])

    if (timeOfDay === "morning") return hour >= 6 && hour < 12
    if (timeOfDay === "afternoon") return hour >= 12 && hour < 18
    if (timeOfDay === "evening") return hour >= 18 && hour < 24

    return true
  })

  // Group time slots into morning, afternoon, and evening
  const morningSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])
    return hour >= 6 && hour < 12
  })

  const afternoonSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])
    return hour >= 12 && hour < 18
  })

  const eveningSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])
    return hour >= 18 && hour < 24
  })

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Saat Seçin</h2>
        <p className="text-muted-foreground">
          Randevunuz için uygun bir saat seçin
        </p>
      </div>

      {/* Time of day filter */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={timeOfDay === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("all")}
          className="flex-1"
        >
          Tümü
        </Button>
        <Button
          variant={timeOfDay === "morning" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("morning")}
          className="flex-1"
          disabled={morningSlots.length === 0}
        >
          Sabah
        </Button>
        <Button
          variant={timeOfDay === "afternoon" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("afternoon")}
          className="flex-1"
          disabled={afternoonSlots.length === 0}
        >
          Öğleden Sonra
        </Button>
        <Button
          variant={timeOfDay === "evening" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("evening")}
          className="flex-1"
          disabled={eveningSlots.length === 0}
        >
          Akşam
        </Button>
      </div>

      {/* Time slots */}
      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2 max-h-[40vh] overflow-y-auto pr-1">
        {filteredTimeSlots.length > 0 ? (
          filteredTimeSlots.map((slot, index) => (
            <motion.div
              key={slot.time}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, delay: index * 0.01 }}
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      onClick={() => slot.isAvailable && onSelect(slot.time)}
                      className={cn(
                        "border rounded-lg p-3 text-center cursor-pointer transition-all",
                        selectedTime === slot.time
                          ? "border-primary bg-primary text-primary-foreground shadow-sm"
                          : slot.isAvailable
                            ? "hover:border-primary/50 hover:bg-muted/50"
                            : "opacity-50 cursor-not-allowed bg-muted/30"
                      )}
                    >
                      <div className="text-sm font-medium">
                        {slot.time}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    {slot.isAvailable ? "Müsait" : slot.reason}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </motion.div>
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground col-span-full">
            <Clock className="h-12 w-12 mx-auto mb-3 opacity-20" />
            <p>Seçilen zaman diliminde müsait saat bulunamadı.</p>
          </div>
        )}
      </div>

      <div className="pt-4 flex flex-col sm:flex-row gap-3">
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-1/2"
          size="lg"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Geri
        </Button>

        {selectedTime && (
          <Button
            onClick={() => onSelect(selectedTime)}
            className="w-full sm:w-1/2"
            size="lg"
          >
            Devam Et
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}
