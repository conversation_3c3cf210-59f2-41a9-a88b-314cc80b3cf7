"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, <PERSON>R<PERSON>, ArrowLeft, Info } from "lucide-react"
import { format, parse, addMinutes } from "date-fns"
import { Moon } from "lucide-react"
import { tr } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { BarberWorkingHours } from "@/lib/db/types"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface TimeSlot {
  time: string;
  isAvailable: boolean;
  reason?: string;
}

interface TimeSelectorProps {
  date: Date | null
  availableTimes: string[]
  selectedTime: string | undefined
  onSelect: (time: string) => void
  barberWorkingHours?: BarberWorkingHours[]
  existingAppointmentTimes?: string[]
  onBack: () => void
}

export function TimeSelector({
  date,
  availableTimes,
  selectedTime,
  onSelect,
  barberWorkingHours = [],
  existingAppointmentTimes = [],
  onBack
}: TimeSelectorProps) {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [timeOfDay, setTimeOfDay] = useState<"morning" | "afternoon" | "evening" | "all">("all")

  // Check if working hours cross midnight
  const crossesMidnight = barberWorkingHours.length > 0 && date ? (() => {
    const dayOfWeek = date.getDay()
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)
    if (!workingHoursForDay || workingHoursForDay.is_closed) return false

    const openTime = workingHoursForDay.open_time.substring(0, 5)
    const closeTime = workingHoursForDay.close_time.substring(0, 5)
    return closeTime < openTime
  })() : false

  // Get close hour for next day slot detection
  const closeHour = barberWorkingHours.length > 0 && date ? (() => {
    const dayOfWeek = date.getDay()
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)
    if (!workingHoursForDay || workingHoursForDay.is_closed) return 0

    const closeTime = workingHoursForDay.close_time.substring(0, 5)
    return parseInt(closeTime.split(':')[0])
  })() : 0

  // Function to check if a time slot is next day
  const isNextDaySlot = (time: string) => {
    if (!crossesMidnight) return false
    const timeSlotHour = parseInt(time.split(':')[0])
    return timeSlotHour <= closeHour
  }

  // Generate time slots
  useEffect(() => {
    console.log("TimeSelector - availableTimes:", availableTimes)

    const slots: TimeSlot[] = []

    // If no barber working hours or date, don't generate slots
    if (!barberWorkingHours.length || !date) {
      setTimeSlots(slots)
      return
    }

    const dayOfWeek = date.getDay()
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

    // If barber is closed on this day, don't generate slots
    if (!workingHoursForDay || workingHoursForDay.is_closed) {
      setTimeSlots(slots)
      return
    }

    const startTimeStr = workingHoursForDay.open_time.substring(0, 5)
    const endTimeStr = workingHoursForDay.close_time.substring(0, 5)

    // Check if working hours cross midnight
    const crossesMidnight = endTimeStr < startTimeStr

    // Generate time slots based on available times if provided, otherwise generate all possible slots
    if (availableTimes.length > 0) {
      // Use available times directly
      availableTimes.forEach(timeString => {
        slots.push({
          time: timeString,
          isAvailable: true,
          reason: ""
        })
      })

      // Add existing appointment times as unavailable slots for display
      existingAppointmentTimes.forEach(timeString => {
        if (!availableTimes.includes(timeString)) {
          slots.push({
            time: timeString,
            isAvailable: false,
            reason: "Dolu randevu"
          })
        }
      })

      // Sort slots by time
      slots.sort((a, b) => {
        if (crossesMidnight) {
          // For midnight crossing, sort same day slots first, then next day slots
          const closeHour = parseInt(endTimeStr.split(':')[0])
          const aHour = parseInt(a.time.split(':')[0])
          const bHour = parseInt(b.time.split(':')[0])

          // Next day slots are those <= close hour (e.g., if close is 02:00, then 00:00, 01:00, 02:00 are next day)
          const aIsNextDay = aHour <= closeHour
          const bIsNextDay = bHour <= closeHour

          if (aIsNextDay !== bIsNextDay) {
            return aIsNextDay ? 1 : -1 // Same day slots first
          }

          // Within same category, sort by time
          if (aIsNextDay && bIsNextDay) {
            return a.time.localeCompare(b.time)
          } else if (!aIsNextDay && !bIsNextDay) {
            return a.time.localeCompare(b.time)
          }
        }

        return a.time.localeCompare(b.time)
      })
    } else {
      // Generate all possible slots within working hours
      if (crossesMidnight) {
        // Working hours cross midnight (e.g., 08:00-02:00)
        // First generate slots from open time to 23:59
        let currentTime = parse(startTimeStr, "HH:mm", new Date())
        const midnight = parse("23:59", "HH:mm", new Date())

        while (currentTime <= midnight) {
          const timeString = format(currentTime, "HH:mm")
          const hasExistingAppointment = existingAppointmentTimes.includes(timeString)

          slots.push({
            time: timeString,
            isAvailable: !hasExistingAppointment,
            reason: hasExistingAppointment ? "Dolu randevu" : ""
          })

          currentTime = addMinutes(currentTime, 30)
        }

        // Then generate slots from 00:00 to close time (next day)
        currentTime = parse("00:00", "HH:mm", new Date())
        const endTime = parse(endTimeStr, "HH:mm", new Date())

        while (currentTime <= endTime) {
          const timeString = format(currentTime, "HH:mm")
          const hasExistingAppointment = existingAppointmentTimes.includes(timeString)

          slots.push({
            time: timeString,
            isAvailable: !hasExistingAppointment,
            reason: hasExistingAppointment ? "Dolu randevu" : ""
          })

          currentTime = addMinutes(currentTime, 30)
        }
      } else {
        // Normal working hours within same day
        let currentTime = parse(startTimeStr, "HH:mm", new Date())
        const endTime = parse(endTimeStr, "HH:mm", new Date())

        while (currentTime <= endTime) {
          const timeString = format(currentTime, "HH:mm")
          const hasExistingAppointment = existingAppointmentTimes.includes(timeString)

          slots.push({
            time: timeString,
            isAvailable: !hasExistingAppointment,
            reason: hasExistingAppointment ? "Dolu randevu" : ""
          })

          currentTime = addMinutes(currentTime, 30)
        }
      }
    }

    setTimeSlots(slots)
  }, [date, availableTimes, barberWorkingHours, existingAppointmentTimes, selectedTime])

  // Filter time slots based on time of day
  const filteredTimeSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])

    if (timeOfDay === "morning") return hour >= 6 && hour < 12
    if (timeOfDay === "afternoon") return hour >= 12 && hour < 18
    if (timeOfDay === "evening") return hour >= 18 && hour < 24

    return true
  })

  // Group time slots into morning, afternoon, and evening
  const morningSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])
    return hour >= 6 && hour < 12
  })

  const afternoonSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])
    return hour >= 12 && hour < 18
  })

  const eveningSlots = timeSlots.filter(slot => {
    const hour = parseInt(slot.time.split(":")[0])
    return hour >= 18 && hour < 24
  })

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Saat Seçin</h2>
        <p className="text-muted-foreground">
          Randevunuz için uygun bir saat seçin
        </p>
      </div>

      {/* Time of day filter */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={timeOfDay === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("all")}
          className="flex-1"
        >
          Tümü
        </Button>
        <Button
          variant={timeOfDay === "morning" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("morning")}
          className="flex-1"
          disabled={morningSlots.length === 0}
        >
          Sabah
        </Button>
        <Button
          variant={timeOfDay === "afternoon" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("afternoon")}
          className="flex-1"
          disabled={afternoonSlots.length === 0}
        >
          Öğleden Sonra
        </Button>
        <Button
          variant={timeOfDay === "evening" ? "default" : "outline"}
          size="sm"
          onClick={() => setTimeOfDay("evening")}
          className="flex-1"
          disabled={eveningSlots.length === 0}
        >
          Akşam
        </Button>
      </div>

      {/* Time slots */}
      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2 max-h-[40vh] overflow-y-auto pr-1">
        {filteredTimeSlots.length > 0 ? (
          filteredTimeSlots.map((slot, index) => (
            <motion.div
              key={slot.time}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, delay: index * 0.01 }}
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      onClick={() => slot.isAvailable && onSelect(slot.time)}
                      className={cn(
                        "border rounded-lg p-3 text-center cursor-pointer transition-all relative",
                        selectedTime === slot.time
                          ? "border-primary bg-primary text-primary-foreground shadow-sm"
                          : slot.isAvailable
                            ? "hover:border-primary/50 hover:bg-muted/50"
                            : "opacity-50 cursor-not-allowed bg-muted/30",
                        isNextDaySlot(slot.time) && "border-blue-200 bg-blue-50/50"
                      )}
                    >
                      <div className="text-sm font-medium flex items-center justify-center gap-1">
                        {slot.time}
                        {isNextDaySlot(slot.time) && (
                          <Moon className="h-3 w-3 text-blue-600" />
                        )}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    {slot.isAvailable
                      ? (isNextDaySlot(slot.time) ? "Müsait (Ertesi gün)" : "Müsait")
                      : slot.reason
                    }
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </motion.div>
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground col-span-full">
            <Clock className="h-12 w-12 mx-auto mb-3 opacity-20" />
            <p>Seçilen zaman diliminde müsait saat bulunamadı.</p>
          </div>
        )}
      </div>

      <div className="pt-4 flex flex-col sm:flex-row gap-3">
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-1/2"
          size="lg"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Geri
        </Button>

        {selectedTime && (
          <Button
            onClick={() => onSelect(selectedTime)}
            className="w-full sm:w-1/2"
            size="lg"
          >
            Devam Et
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}
