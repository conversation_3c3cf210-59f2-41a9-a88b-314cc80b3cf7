"use client"

import { useState, useEffect } from "react"
import { format, parse, addMinutes } from "date-fns"
import { tr } from "date-fns/locale"
import Link from "next/link"
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, useSensor, useSensors, PointerSensor, TouchSensor } from "@dnd-kit/core"
import { toast } from "sonner"

import { cn, checkAppointmentOverlap } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Appointment, BarberWorkingHours } from "@/lib/db/types"
import { HolidayDate } from "@/components/ui/holiday-calendar"
import { DraggableAppointment } from "@/components/calendar/DraggableAppointment"
import { DroppableTimeSlot } from "@/components/calendar/DroppableTimeSlot"
import { appointments as appointmentsApi } from "@/lib/db"
import { DailyCalendarSkeleton } from "@/components/ui/skeleton-loaders"

// Extended Appointment type to include joined data from the API
interface AppointmentWithJoins extends Appointment {
  customers?: {
    name: string;
    surname: string;
    phone: string;
  };
  barbers?: {
    name: string;
  };
  services?: {
    name: string;
    duration: number;
  };
}

interface DailyCalendarViewProps {
  selectedDate: Date
  appointments: AppointmentWithJoins[]
  isLoading: boolean
  onSlotClick?: (date: Date, time: string) => void
  holidayDates?: HolidayDate[]
  barberWorkingHours?: BarberWorkingHours[]
  onAppointmentUpdated?: () => void
}

export function DailyCalendarView({
  selectedDate,
  appointments,
  isLoading,
  onSlotClick,
  holidayDates = [],
  barberWorkingHours = [],
  onAppointmentUpdated
}: DailyCalendarViewProps) {
  // Generate time slots from 9:00 to 19:00 with 30-minute intervals
  const [timeSlots, setTimeSlots] = useState<string[]>([])
  const [activeAppointment, setActiveAppointment] = useState<AppointmentWithJoins | null>(null)
  const isMobile = useIsMobile()

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // Increase distance for mobile to prevent accidental drags
        distance: isMobile ? 12 : 8,
        // Add delay for mobile to prevent accidental drags during scroll
        delay: isMobile ? 150 : 0,
        // Tolerance helps with diagonal movements
        tolerance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 150,
        tolerance: 8,
      },
    })
  )

  useEffect(() => {
    const slots = []

    // Varsayılan başlangıç ve bitiş saatleri
    let startTimeStr = "08:00"
    let endTimeStr = "23:59"

    // Eğer berber çalışma saatleri varsa, onları kullan
    if (barberWorkingHours.length > 0) {
      const dayOfWeek = selectedDate.getDay()
      const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

      if (workingHoursForDay && !workingHoursForDay.is_closed) {
        startTimeStr = workingHoursForDay.open_time.substring(0, 5)
        endTimeStr = workingHoursForDay.close_time.substring(0, 5)
      }
    }

    const startTime = parse(startTimeStr, "HH:mm", new Date())
    const endTime = parse(endTimeStr, "HH:mm", new Date())
    let currentTime = startTime

    while (currentTime <= endTime) {
      slots.push(format(currentTime, "HH:mm"))
      currentTime = addMinutes(currentTime, 30)
    }

    setTimeSlots(slots)
  }, [selectedDate, barberWorkingHours])

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const { appointment } = active.data.current as { appointment: AppointmentWithJoins }
    setActiveAppointment(appointment)
  }

  // Handle drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    setActiveAppointment(null)

    // If no drop target, do nothing
    if (!over) return

    const appointmentId = active.id as string
    const appointment = appointments.find(apt => apt.id === appointmentId)

    if (!appointment) return

    // Sadece aktif randevular taşınabilir
    if (appointment.status !== 'booked') {
      let statusMessage = "";
      switch (appointment.status) {
        case 'cancelled':
          statusMessage = "İptal edilmiş";
          break;
        case 'completed':
          statusMessage = "Tamamlanmış";
          break;
        case 'no-show':
          statusMessage = "Gelmemiş";
          break;
        default:
          statusMessage = "Bu durumdaki";
      }
      toast.error(`${statusMessage} randevular taşınamaz`)
      return
    }

    // Get the source time from the appointment
    const sourceTime = appointment.start_time.substring(0, 5)

    const { date, time, isAvailable } = over.data.current as {
      date: Date,
      time: string,
      isAvailable: boolean
    }

    // If the time slot is not available, do nothing
    if (!isAvailable) {
      toast.error("Bu zaman dilimine randevu taşınamaz")
      return
    }

    try {
      // Calculate end time based on service duration
      const serviceDuration = appointment.services?.duration || 30
      const startTime = parse(time, "HH:mm", new Date())
      const endTime = addMinutes(startTime, serviceDuration)
      const endTimeStr = format(endTime, "HH:mm")

      // If the appointment is dropped on the same time slot, do nothing
      if (sourceTime === time) {
        return
      }

      // Format date for database
      const formattedDate = format(date, "yyyy-MM-dd")

      // Check for appointment overlaps
      const hasOverlap = checkAppointmentOverlap(
        appointment,
        formattedDate,
        time,
        endTimeStr,
        appointments
      )

      if (hasOverlap) {
        toast.error("Bu berberin seçilen saat aralığında başka bir randevusu var")
        return
      }

      // Update appointment in database
      await appointmentsApi.updateAppointment({
        id: appointmentId,
        date: formattedDate,
        start_time: time,
        end_time: endTimeStr
      })

      toast.success("Randevu başarıyla taşındı")

      // Notify parent component to refresh appointments
      if (onAppointmentUpdated) {
        onAppointmentUpdated()
      }
    } catch (error) {
      console.error("Error updating appointment:", error)
      toast.error("Randevu taşınırken bir hata oluştu")
    }
  }



  if (isLoading) {
    return <DailyCalendarSkeleton />;
  }

  // Function to check if a date is a holiday
  const isHoliday = (date: Date) => {
    return holidayDates.some(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    )
  }

  // Function to get holiday description
  const getHolidayDescription = (date: Date) => {
    const holiday = holidayDates.find(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    )
    return holiday?.description || "Tatil Günü"
  }

  // Function to check if a barber works on a specific day
  const isBarberWorkingDay = (date: Date) => {
    if (!barberWorkingHours.length) return true

    const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

    // If no working hours found for this day or is_closed is true, barber doesn't work
    return workingHoursForDay && !workingHoursForDay.is_closed
  }

  // Function to check if a time is within barber working hours
  const isTimeWithinWorkingHours = (time: string) => {
    if (!barberWorkingHours.length) return true

    const dayOfWeek = selectedDate.getDay()
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

    if (!workingHoursForDay || workingHoursForDay.is_closed) return false

    return time >= workingHoursForDay.open_time && time <= workingHoursForDay.close_time
  }

  // Function to check if a time slot has an appointment
  const getAppointmentForTimeSlot = (time: string) => {
    return appointments.find(apt => apt.start_time.substring(0, 5) === time)
  }

  // Check if the selected date is a holiday
  const isHolidayDate = isHoliday(selectedDate)

  // Check if the barber works on the selected date
  const isBarberWorking = isBarberWorkingDay(selectedDate)

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-2">
        <Card className={cn(
          isHolidayDate && "border-red-500 dark:border-red-700",
          !isBarberWorking && barberWorkingHours.length > 0 && "border-yellow-500 dark:border-yellow-700"
        )}>
          <CardHeader className="py-3">
            <div className="flex justify-between items-center">
              <CardTitle>
                {format(selectedDate, "EEEE, d MMMM yyyy", { locale: tr })}
              </CardTitle>
              <div className="flex gap-2">
                {isHolidayDate && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge variant="destructive">Tatil</Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{getHolidayDescription(selectedDate)}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
                {!isBarberWorking && barberWorkingHours.length > 0 && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge variant="outline" className="bg-yellow-100 dark:bg-yellow-900/30">Çalışma Dışı</Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Berber bu gün çalışmıyor</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
            {(isHolidayDate || !isBarberWorking) && (
              <p className="text-sm text-muted-foreground mt-2">
                {isHolidayDate ? "Tatil günü - Randevu alınamaz" : "Berber çalışmıyor - Randevu alınamaz"}
              </p>
            )}
          </CardHeader>
          <CardContent className={cn(
            "space-y-2",
            // Mobil görünümde scroll ekle
            isMobile && "max-h-[60vh] overflow-y-auto scrollbar-hide"
          )}>
            {timeSlots.map((time) => {
              const appointment = getAppointmentForTimeSlot(time)
              const isTimeAvailable = !isHolidayDate && isBarberWorking && isTimeWithinWorkingHours(time)
              const timeUnavailableReason = isHolidayDate
                ? "Tatil günü"
                : !isBarberWorking
                  ? "Berber çalışmıyor"
                  : !isTimeWithinWorkingHours(time)
                    ? "Çalışma saatleri dışında"
                    : ""

              const slotId = `slot-${format(selectedDate, "yyyy-MM-dd")}-${time}`

              return (
                <DroppableTimeSlot
                  key={time}
                  id={slotId}
                  date={selectedDate}
                  time={time}
                  isAvailable={(isTimeAvailable && !appointment) ? true : false}
                  unavailableReason={timeUnavailableReason}
                  className={appointment ? "bg-muted" : ""}
                >
                  <div className="font-medium">{time}</div>

                  {appointment ? (
                    <div className="mt-2">
                      <DraggableAppointment
                        appointment={appointment}
                        onAppointmentUpdated={onAppointmentUpdated}
                      />
                    </div>
                  ) : (
                    <div
                      className="text-muted-foreground text-sm mt-1 cursor-pointer"
                      onClick={() => {
                        if (isTimeAvailable && onSlotClick) {
                          console.log("DailyCalendarView - Slot clicked:", { selectedDate, time });
                          onSlotClick(selectedDate, time);
                        }
                      }}
                    >
                      {isTimeAvailable ? (
                        "Müsait - Randevu eklemek için tıklayın"
                      ) : (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-red-500 dark:text-red-400">{timeUnavailableReason}</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Bu saatte randevu oluşturulamaz</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  )}
                </DroppableTimeSlot>
              )
            })}
          </CardContent>
        </Card>
      </div>

      {/* Drag overlay for visual feedback during dragging */}
      <DragOverlay>
        {activeAppointment && (
          <DraggableAppointment
            appointment={activeAppointment}
            isDraggingEnabled={false}
            onAppointmentUpdated={onAppointmentUpdated}
          />
        )}
      </DragOverlay>
    </DndContext>
  )
}
