import { supabaseClient } from '@/lib/supabase-singleton';


const supabase = supabaseClient;

/**
 * Upload a file to Supabase Storage
 * @param bucket - Storage bucket name
 * @param path - Path within the bucket
 * @param file - File to upload
 * @returns Public URL of the uploaded file
 */
export async function uploadFile(bucket: string, path: string, file: File) {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: true
    });

  if (error) throw error;

  // Get public URL
  const { data: publicUrlData } = supabase.storage
    .from(bucket)
    .getPublicUrl(data.path);

  return publicUrlData.publicUrl;
}

/**
 * Delete a file from Supabase Storage
 * @param bucket - Storage bucket name
 * @param path - Path within the bucket
 */
export async function deleteFile(bucket: string, path: string) {
  const { error } = await supabase.storage
    .from(bucket)
    .remove([path]);

  if (error) throw error;
  return true;
}

/**
 * Upload a product image
 * @param salonId - Salon ID
 * @param productId - Product ID
 * @param file - Image file
 * @returns Public URL of the uploaded image
 */
export async function uploadProductImage(salonId: string, productId: string, file: File) {
  // Generate a unique filename and timestamp
  const fileExt = file.name.split('.').pop();
  const timestamp = Date.now();

  // Simplify the path structure to avoid RLS issues
  // Use a flat structure with a naming convention that includes salon and product IDs
  const filePath = `${salonId}_${productId}_${timestamp}.${fileExt}`;

  return uploadFile('product_images', filePath, file);
}

/**
 * Delete a product image
 * @param path - Full path of the image
 */
export async function deleteProductImage(path: string) {
  // Extract the filename from the URL
  // The URL format is like: https://rbyfjjtqitfkrddzuxmt.supabase.co/storage/v1/object/product_images/salonId_productId_timestamp.ext
  const urlParts = path.split('/');
  const filename = urlParts[urlParts.length - 1];

  return deleteFile('product_images', filename);
}

/**
 * Get storage usage for a salon
 * @param salonId - Salon ID
 * @returns Total storage usage in bytes
 */
export async function getSalonStorageUsage(salonId: string) {
  // List all files in the bucket
  const { data, error } = await supabase.storage
    .from('product_images')
    .list('', {
      sortBy: { column: 'name', order: 'asc' }
    });

  if (error) throw error;

  // Filter files that belong to this salon (filename starts with salonId_)
  const salonFiles = data.filter(file =>
    file.name.startsWith(`${salonId}_`)
  );

  // Calculate total size
  let totalSize = 0;
  for (const file of salonFiles) {
    totalSize += file.metadata.size || 0;
  }

  return totalSize;
}

/**
 * Check if salon has reached storage limit
 * @param salonId - Salon ID
 * @param limitMB - Storage limit in MB
 * @returns Boolean indicating if limit is reached
 */
export async function hasReachedStorageLimit(salonId: string, limitMB: number) {
  // If limit is 0, no storage is allowed
  if (limitMB === 0) return true;

  const usage = await getSalonStorageUsage(salonId);
  const usageMB = usage / (1024 * 1024);

  return usageMB >= limitMB;
}

/**
 * Process image before upload (resize and compress)
 * @param file - Original image file
 * @param maxWidth - Maximum width
 * @param maxHeight - Maximum height
 * @param quality - JPEG quality (0-1)
 * @returns Processed image as File object
 */
export async function processImage(
  file: File,
  maxWidth = 1200,
  maxHeight = 1200,
  quality = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      // Calculate new dimensions
      let width = img.width;
      let height = img.height;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // Create canvas and draw resized image
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      ctx.drawImage(img, 0, 0, width, height);

      // Convert to WebP if supported
      const supportsWebP = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      const mimeType = supportsWebP ? 'image/webp' : 'image/jpeg';

      // Convert to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Could not create blob'));
            return;
          }

          // Create new file
          const newFile = new File(
            [blob],
            file.name.replace(/\.[^/.]+$/, supportsWebP ? '.webp' : '.jpg'),
            { type: mimeType }
          );

          resolve(newFile);
        },
        mimeType,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('Could not load image'));
    };

    img.src = URL.createObjectURL(file);
  });
}
