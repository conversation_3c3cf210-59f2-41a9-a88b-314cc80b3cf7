import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için hizmet bilgilerini salon ID ile getir
 */
export async function getPublicServicesBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_services_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;

  // Sort by sort_order if available
  if (data && Array.isArray(data)) {
    return data.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  }

  return data;
}
